// middleware/financeMiddleware.js
const User = require('../models/User');
const StaffPermission = require('../models/StaffPermission');

/**
 * Middleware to check if user has finance permissions
 * Allows: admin, super, bursar, school_admin, or staff with manage_school_credit_balance permission
 */
const checkFinancePermissions = async (req, res, next) => {
  try {
    const user = req.user; // User should be attached by authenticate middleware

    if (!user) {
      return res.status(401).json({ message: 'User not found' });
    }

    // Allow super admins and school admins
    if (user.role === 'super' || user.role === 'admin') {
      return next();
    }

    // Allow bursar role (has finance permissions by default)
    if (user.role === 'bursar') {
      return next();
    }

    // Allow school_admin role (has finance permissions by default)
    if (user.role === 'school_admin') {
      return next();
    }

    // For other staff roles, check specific permissions
    if (['teacher', 'dean_of_studies'].includes(user.role)) {
      // Get the school_id from request body or params
      const school_id = req.body.school_id || req.params.school_id;
      
      if (!school_id) {
        return res.status(400).json({ message: 'School ID is required' });
      }

      // Check if user has finance permissions for this school
      const staffPermission = await StaffPermission.findOne({
        user_id: user._id,
        school_id: school_id
      });

      if (!staffPermission) {
        return res.status(403).json({ 
          message: 'No permissions found for this school' 
        });
      }

      // Check if user has manage_school_credit_balance permission
      if (!staffPermission.permissions.financials.manage_school_credit_balance) {
        return res.status(403).json({ 
          message: 'Insufficient permissions. Finance permissions required.' 
        });
      }

      return next();
    }

    // If none of the above conditions are met, deny access
    return res.status(403).json({ 
      message: 'Insufficient permissions. Finance permissions required.' 
    });

  } catch (error) {
    console.error('Error checking finance permissions:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
};

/**
 * Middleware to check if user can manage school credits
 * More specific than checkFinancePermissions - only for credit management
 */
const checkCreditManagementPermissions = async (req, res, next) => {
  try {
    const user = req.user;

    if (!user) {
      return res.status(401).json({ message: 'User not found' });
    }

    // Allow super admins
    if (user.role === 'super') {
      return next();
    }

    // For school-level operations, check if user belongs to the school
    const school_id = req.body.school_id || req.params.school_id;
    
    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Check if user is associated with this school
    if (!user.school_ids || !user.school_ids.includes(school_id)) {
      return res.status(403).json({ 
        message: 'User not associated with this school' 
      });
    }

    // Allow admin role (school admin)
    if (user.role === 'admin') {
      return next();
    }

    // Allow bursar and school_admin roles
    if (['bursar', 'school_admin'].includes(user.role)) {
      return next();
    }

    // For other staff roles, check specific permissions
    if (['teacher', 'dean_of_studies'].includes(user.role)) {
      const staffPermission = await StaffPermission.findOne({
        user_id: user._id,
        school_id: school_id
      });

      if (!staffPermission) {
        return res.status(403).json({ 
          message: 'No permissions found for this school' 
        });
      }

      // Check if user has manage_school_credit_balance permission
      if (!staffPermission.permissions.financials.manage_school_credit_balance) {
        return res.status(403).json({ 
          message: 'Insufficient permissions. Credit management permissions required.' 
        });
      }

      return next();
    }

    // If none of the above conditions are met, deny access
    return res.status(403).json({ 
      message: 'Insufficient permissions. Credit management permissions required.' 
    });

  } catch (error) {
    console.error('Error checking credit management permissions:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
};

/**
 * Helper function to check if user has specific finance permission
 */
const hasFinancePermission = async (user_id, school_id, permission) => {
  try {
    const user = await User.findById(user_id);
    
    if (!user) {
      return false;
    }

    // Super admins have all permissions
    if (user.role === 'super') {
      return true;
    }

    // School admins and bursars have finance permissions by default
    if (['admin', 'school_admin', 'bursar'].includes(user.role)) {
      return true;
    }

    // Check staff permissions
    const staffPermission = await StaffPermission.findOne({
      user_id: user_id,
      school_id: school_id
    });

    if (!staffPermission) {
      return false;
    }

    return staffPermission.permissions.financials[permission] || false;
  } catch (error) {
    console.error('Error checking finance permission:', error);
    return false;
  }
};

module.exports = {
  checkFinancePermissions,
  checkCreditManagementPermissions,
  hasFinancePermission
};
