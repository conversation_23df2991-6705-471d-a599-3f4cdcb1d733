import { BASE_API_URL } from "./AuthContext";
import { getTokenFromCookie } from "./UserServices";

// Interface pour les revenus totaux
export interface TotalRevenueResponse {
  totalRevenue: number;
  totalTransactions: number;
  totalCredits: number;
}

// Interface pour les changements de revenus
export interface RevenueChangeResponse {
  totalAmount: number;
  percentageChange: number;
  currentMonthTransactions: number;
  previousMonthTransactions: number;
  currentMonthRevenue: number;
  previousMonthRevenue: number;
}

/**
 * Obtenir le total des revenus des achats de crédits (seulement les transactions completed)
 */
export async function getTotalRevenue(): Promise<TotalRevenueResponse> {
  try {
    const token = getTokenFromCookie("idToken");
    
    if (!token) {
      throw new Error("No authentication token found");
    }

    const response = await fetch(`${BASE_API_URL}/credit-purchase/revenue/total`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Failed to fetch total revenue: ${response.status}`);
    }

    const data = await response.json();
    return {
      totalRevenue: data.totalRevenue || 0,
      totalTransactions: data.totalTransactions || 0,
      totalCredits: data.totalCredits || 0
    };
  } catch (error: any) {
    console.error("Error fetching total revenue:", error);
    throw new Error(error.message || "Failed to fetch total revenue");
  }
}

/**
 * Obtenir les revenus du mois actuel avec changement en pourcentage par rapport au mois précédent
 */
export async function getRevenueChange(): Promise<RevenueChangeResponse> {
  try {
    const token = getTokenFromCookie("idToken");
    
    if (!token) {
      throw new Error("No authentication token found");
    }

    const response = await fetch(`${BASE_API_URL}/credit-purchase/revenue/change`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Failed to fetch revenue change: ${response.status}`);
    }

    const data = await response.json();
    return {
      totalAmount: data.totalAmount || 0,
      percentageChange: data.percentageChange || 0,
      currentMonthTransactions: data.currentMonthTransactions || 0,
      previousMonthTransactions: data.previousMonthTransactions || 0,
      currentMonthRevenue: data.currentMonthRevenue || 0,
      previousMonthRevenue: data.previousMonthRevenue || 0
    };
  } catch (error: any) {
    console.error("Error fetching revenue change:", error);
    throw new Error(error.message || "Failed to fetch revenue change");
  }
}

/**
 * Formater un montant en devise XAF
 */
export function formatRevenue(amount: number): string {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'XAF',
    minimumFractionDigits: 0,
  }).format(amount);
}

/**
 * Formater un nombre de transactions
 */
export function formatTransactionCount(count: number): string {
  return `${count.toLocaleString()} transaction${count > 1 ? 's' : ''}`;
}

/**
 * Formater un nombre de crédits
 */
export function formatCreditCount(count: number): string {
  return `${count.toLocaleString()} crédit${count > 1 ? 's' : ''}`;
}
