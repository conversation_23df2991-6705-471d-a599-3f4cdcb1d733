'use client';

import { useSearchParams } from 'next/navigation';
import { <PERSON><PERSON>heck, AlertCircle } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { verifyStudentId } from '../services/StudentServices'; // update path
import { StudentSchema } from '../models/StudentModel';

export default function VerifyStudentStatic() {
  const searchParams = useSearchParams();
  const id = searchParams.get('id') || "6844b50058e70f9151569b06";

  const [student, setStudent] = useState<StudentSchema | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!id) {
      setError('No student ID provided in the URL.');
      return;
    }

    setLoading(true);
    verifyStudentId(id)
      .then((data) => {
        setStudent(data);
        setError(null);
      })
      .catch(() => {
        setError('Failed to verify student ID.');
      })
      .finally(() => {
        setLoading(false);
      });
  }, [id]);

  if (loading) {
    return (
      <div className="p-10 text-center text-gray-600">
        <p>Loading verification data...</p>
      </div>
    );
  }

  if (error || !student) {
    return (
      <div className="p-10 text-center text-red-600">
        <AlertCircle size={32} className="mx-auto mb-2" />
        <h1 className="text-2xl font-bold">Verification Failed</h1>
        <p>{error || 'This QR code does not contain valid student information.'}</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-blue-50 p-6">
      <ShieldCheck size={48} className="text-blue-600 mb-4" />
      <h1 className="text-2xl font-bold text-blue-700 mb-2">Student ID Verified</h1>
      <p className="text-gray-700 mb-6">
        This student ID card is valid and has been digitally signed.
      </p>

      <div className="bg-white shadow-md rounded-lg p-6 max-w-sm w-full text-center">
        <h2 className="text-xl font-semibold text-gray-800">{student.name}</h2>
        <p className="text-sm text-gray-500 mb-2">
          Student ID: <strong>{student.student_id}</strong>
        </p>
        <p className="text-sm text-gray-500 italic">Verified at time of issuance</p>
      </div>
    </div>
  );
}
