"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Save, Loader2 } from 'lucide-react';
import { FeeSchema, FeeCreateSchema, FeeUpdateSchema } from '@/app/models/FeesModel';
import useAuth from '@/app/hooks/useAuth';

interface FeeTypeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: FeeCreateSchema | FeeUpdateSchema) => Promise<void>;
  fee?: FeeSchema | null;
  isSubmitting?: boolean;
}

const FeeTypeModal: React.FC<FeeTypeModalProps> = ({
  isOpen,
  onClose,
  onSave,
  fee,
  isSubmitting = false
}) => {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    fee_type: '',
    amount: '',
    school_id: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const commonFeeTypes = [
    'Tuition Fee',
    'Registration Fee',
    'Library Fee',
    'Laboratory Fee',
    'Sports Fee',
    'Technology Fee',
    'Transportation Fee',
    'Examination Fee',
    'Activity Fee',
    'Uniform Fee',
    'Textbook Fee',
    'Other'
  ];

  // Initialize form data when modal opens or fee changes
  useEffect(() => {
    if (isOpen) {
      if (fee) {
        // Edit mode
        setFormData({
          fee_type: fee.fee_type || '',
          amount: fee.amount?.toString() || '',
          school_id: fee.school_id || ''
        });
      } else {
        // Create mode
        setFormData({
          fee_type: '',
          amount: '',
          school_id: user?.school_ids?.[0] || ''
        });
      }
      setErrors({});
    }
  }, [isOpen, fee, user]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.fee_type.trim()) {
      newErrors.fee_type = 'Fee type is required';
    }

    if (!formData.amount.trim()) {
      newErrors.amount = 'Amount is required';
    } else {
      const amount = parseFloat(formData.amount);
      if (isNaN(amount) || amount < 0) {
        newErrors.amount = 'Please enter a valid positive amount';
      }
    }

    if (!formData.school_id.trim()) {
      newErrors.school_id = 'School ID is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      const amount = parseFloat(formData.amount);

      if (fee) {
        // Update mode
        const updateData: FeeUpdateSchema = {
          _id: fee._id,
          fee_type: formData.fee_type,
          amount: amount,
          school_id: formData.school_id
        };
        await onSave(updateData);
      } else {
        // Create mode
        const createData: FeeCreateSchema = {
          fee_type: formData.fee_type,
          amount: amount,
          school_id: formData.school_id
        };
        await onSave(createData);
      }
    } catch (error) {
      console.error('Error saving fee:', error);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black bg-opacity-50"
          onClick={onClose}
        />

        {/* Modal */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className="relative bg-widget rounded-lg shadow-xl w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-stroke">
            <h2 className="text-xl font-semibold text-text">
              {fee ? 'Edit Fee Type' : 'Add New Fee Type'}
            </h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              disabled={isSubmitting}
            >
              <X className="w-5 h-5 text-text" />
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="p-6 space-y-4">
            {/* Fee Type Field */}
            {/* Fee Type Field */}
            <div>
              <label htmlFor="fee_type" className="block text-sm font-medium text-text mb-2">
                Fee Type <span className="text-red-500">*</span>
              </label>
              <input
                id="fee_type"
                list="fee-types"
                value={formData.fee_type}
                onChange={(e) => handleInputChange('fee_type', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 bg-widget text-text ${errors.fee_type ? 'border-red-500' : 'border-stroke'
                  }`}
                disabled={isSubmitting}
                placeholder="Select or type fee type"
              />
              <datalist id="fee-types">
                {commonFeeTypes.map((type) => (
                  <option key={type} value={type} />
                ))}
              </datalist>
              {errors.fee_type && (
                <p className="mt-1 text-sm text-red-500">{errors.fee_type}</p>
              )}
            </div>
            {/* Amount Field */}
            <div>
              <label htmlFor="amount" className="block text-sm font-medium text-text mb-2">
                Amount <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text">$</span>
                <input
                  type="number"
                  id="amount"
                  value={formData.amount}
                  onChange={(e) => handleInputChange('amount', e.target.value)}
                  className={`w-full pl-8 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 bg-widget text-text ${errors.amount ? 'border-red-500' : 'border-stroke'
                    }`}
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                  disabled={isSubmitting}
                />
              </div>
              {errors.amount && (
                <p className="mt-1 text-sm text-red-500">{errors.amount}</p>
              )}
            </div>

            {/* School ID Field (hidden, auto-filled) */}
            <input
              type="hidden"
              value={formData.school_id}
            />

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-text bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-2 text-sm font-medium text-white bg-teal hover:bg-teal-600 rounded-lg transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Save className="w-4 h-4" />
                )}
                <span>{isSubmitting ? 'Saving...' : 'Save'}</span>
              </button>
            </div>
          </form>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default FeeTypeModal;
