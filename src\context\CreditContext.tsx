"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import useAuth from '@/app/hooks/useAuth';
import { getSchoolCredits } from '@/app/services/SchoolServices';
import { getSchoolSubscription, getPendingCreditPurchases } from '@/app/services/SubscriptionServices';
import { calculateSchoolCredits } from '@/app/services/CreditServices';
import { SchoolSubscriptionSchema, CreditPurchaseSchema } from '@/app/models/SchoolSubscriptionModel';

interface CreditContextType {
  // Crédits d'école (basés sur School.credit et SchoolSubscription)
  availableCredits: number;
  totalPaid: number;
  creditCount: number;
  totalCreditsPurchased: number;

  // Crédits d'école existants (legacy - pour compatibilité)
  schoolCredits: number;
  schoolCreditsLoading: boolean;

  // Souscription
  subscription: SchoolSubscriptionSchema | null;
  subscriptionLoading: boolean;

  // Paiements en attente
  pendingPurchases: CreditPurchaseSchema[];
  pendingPurchasesLoading: boolean;

  // Actions
  refreshCredits: () => void;
  refreshAll: () => void;

  // État global
  loading: boolean;
  error: string | null;
}

const CreditContext = createContext<CreditContextType | undefined>(undefined);

interface CreditProviderProps {
  children: ReactNode;
}

export function CreditProvider({ children }: CreditProviderProps) {
  const { user } = useAuth();

  // États pour les crédits calculés basés sur Credit.js
  const [availableCredits, setAvailableCredits] = useState<number>(0);
  const [totalPaid, setTotalPaid] = useState<number>(0);
  const [creditCount, setCreditCount] = useState<number>(0);
  const [totalCreditsPurchased, setTotalCreditsPurchased] = useState<number>(0);

  // États pour les crédits d'école existants (legacy)
  const [schoolCredits, setSchoolCredits] = useState<number>(0);
  const [schoolCreditsLoading, setSchoolCreditsLoading] = useState(false);

  // États pour la souscription
  const [subscription, setSubscription] = useState<SchoolSubscriptionSchema | null>(null);
  const [subscriptionLoading, setSubscriptionLoading] = useState(false);

  // États pour les paiements en attente
  const [pendingPurchases, setPendingPurchases] = useState<CreditPurchaseSchema[]>([]);
  const [pendingPurchasesLoading, setPendingPurchasesLoading] = useState(false);

  // État global
  const [error, setError] = useState<string | null>(null);

  const schoolId = user?.school_ids?.[0];

  // Fonction pour calculer les crédits basés sur les paiements d'étudiants
  const calculateCreditsFromPayments = async () => {
    if (!schoolId) return;

    try {
      setError(null);

      // Utiliser la fonction du service pour calculer les crédits
      const result = await calculateSchoolCredits(schoolId);

      setTotalPaid(result.totalPaid);
      setAvailableCredits(result.availableCredits);
      setCreditCount(result.creditCount);
      setTotalCreditsPurchased(result.totalCreditsPurchased);

      return result;
    } catch (err) {
      console.error('Error calculating credits from payments:', err);
      setError('Erreur lors du calcul des crédits');
      return { totalPaid: 0, availableCredits: 0, creditCount: 0 };
    }
  };

  // Fonction pour récupérer les crédits d'école existants (legacy)
  const fetchSchoolCredits = async () => {
    if (!schoolId) return;

    try {
      setSchoolCreditsLoading(true);
      setError(null);
      const credits = await getSchoolCredits(schoolId);
      setSchoolCredits(credits);
    } catch (err) {
      console.error('Error fetching school credits:', err);
      // Ne pas afficher d'erreur pour les crédits legacy
    } finally {
      setSchoolCreditsLoading(false);
    }
  };

  // Fonction pour récupérer la souscription basée sur les crédits calculés
  const fetchSubscription = async () => {
    if (!schoolId) return;

    try {
      setSubscriptionLoading(true);
      setError(null);
      const subscription = await getSchoolSubscription(schoolId);
      console.log('Fetched subscription:', subscription); // Debug log
      setSubscription(subscription);
    } catch (err) {
      console.error('Error fetching subscription:', err);
      // Si pas de souscription, on peut en créer une basée sur les crédits calculés
      setSubscription(null);
    } finally {
      setSubscriptionLoading(false);
    }
  };

  // Fonction pour récupérer les paiements en attente
  const fetchPendingPurchases = async () => {
    if (!schoolId) return;

    try {
      setPendingPurchasesLoading(true);
      setError(null);
      const purchases = await getPendingCreditPurchases(schoolId);
      setPendingPurchases(purchases);
    } catch (err) {
      console.error('Error fetching pending purchases:', err);
      setPendingPurchases([]);
    } finally {
      setPendingPurchasesLoading(false);
    }
  };

  // Fonction pour rafraîchir tous les crédits
  const refreshCredits = async () => {
    await calculateCreditsFromPayments();
    await fetchSchoolCredits();
  };

  // Fonction pour rafraîchir tout (crédits + souscription + paiements en attente)
  const refreshAll = async () => {
    await calculateCreditsFromPayments();
    await fetchSchoolCredits();
    await fetchSubscription();
    await fetchPendingPurchases();
  };

  // Charger les données au montage et quand l'utilisateur change
  useEffect(() => {
    if (schoolId) {
      refreshAll();
    }
  }, [schoolId]);

  const loading = schoolCreditsLoading || subscriptionLoading || pendingPurchasesLoading;

  const value: CreditContextType = {
    availableCredits,
    totalPaid,
    creditCount,
    totalCreditsPurchased,
    schoolCredits,
    schoolCreditsLoading,
    subscription,
    subscriptionLoading,
    pendingPurchases,
    pendingPurchasesLoading,
    refreshCredits,
    refreshAll,
    loading,
    error
  };

  return (
    <CreditContext.Provider value={value}>
      {children}
    </CreditContext.Provider>
  );
}

// Hook pour utiliser le contexte
export function useCreditContext(): CreditContextType {
  const context = useContext(CreditContext);
  if (context === undefined) {
    throw new Error('useCreditContext must be used within a CreditProvider');
  }
  return context;
}

// Hook pour les crédits d'école (compatibilité avec l'existant)
export function useSchoolCredits() {
  const { schoolCredits, schoolCreditsLoading, refreshCredits, error } = useCreditContext();
  return {
    credits: schoolCredits,
    loading: schoolCreditsLoading,
    refresh: refreshCredits,
    error
  };
}

// Hook pour les crédits de souscription (basés sur les paiements)
export function useSubscriptionCredits() {
  const {
    availableCredits,
    totalPaid,
    creditCount,
    totalCreditsPurchased,
    subscriptionLoading,
    subscription,
    pendingPurchases,
    pendingPurchasesLoading,
    refreshAll,
    error
  } = useCreditContext();

  return {
    credits: availableCredits,
    availableCredits,
    totalPaid,
    creditCount,
    totalCreditsPurchased,
    loading: subscriptionLoading,
    subscription,
    pendingPurchases,
    pendingPurchasesLoading,
    refresh: refreshAll,
    refreshAll,
    error,
    hasCredits: (amount: number = 1) => availableCredits >= amount,
    isLowBalance: subscription ? availableCredits <= subscription.low_credit_threshold : availableCredits <= 10,
    hasPendingPurchases: pendingPurchases.length > 0
  };
}
