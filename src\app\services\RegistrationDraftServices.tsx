import { BASE_API_URL } from "./AuthContext";
import { getTokenFromCookie } from "./UserServices";
import { RegistrationDraftSchema } from "../models/RegistrationDraft"; // import the type for registration draft

// Get registration drafts for a specific user and school
export async function getRegistrationDrafts(schoolId: string, userId: string): Promise<RegistrationDraftSchema[]> {
    try {
        const token = getTokenFromCookie("idToken");

        // Check if we have a token
        if (!token) {
            console.warn("No authentication token found");
            return []; // Return empty array instead of throwing error
        }

        console.log("Fetching registration drafts from:", `${BASE_API_URL}/registration-draft/get/${schoolId}/user/${userId}`);

        const response = await fetch(`${BASE_API_URL}/registration-draft/get/${schoolId}/user/${userId}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
            },
        });

        if (!response.ok) {
            console.error("API Response Error:", {
                status: response.status,
                statusText: response.statusText,
                url: response.url,
            });

            if (response.status === 401) {
                console.warn("Unauthorized access - user may need to log in");
                return [];
            }

            throw new Error(`Failed to fetch registration drafts: ${response.status} ${response.statusText}`);
        }
       const data = await response.json();

        // Access the drafts array from the response
        const draftsList = data.drafts || [];

        if (!Array.isArray(draftsList)) {
            console.error("Expected array but got:", typeof draftsList);
            return [];
        }

        return draftsList.map((item: any) => ({
            _id: item._id,
            user_id: item.user_id,
            school_id: item.school_id,
            currentStep: item.currentStep,
            isCompleted: item.isCompleted,
            status: item.status,
            firstName: item.firstName,
            lastName: item.lastName,
            middleName: item.middleName,
            dateOfBirth: new Date(item.dateOfBirth),
            nationality: item.nationality,
            gender: item.gender,
            place_of_birth: item.place_of_birth,
            address: item.address,
            student_phone: item.student_phone,
            student_country_code: item.student_country_code,
            guardian_address: item.guardian_address,
            guardian_phone: item.guardian_phone,
            guardian_country_code: item.guardian_country_code,
            guardian_name: item.guardian_name,
            guardian_occupation: item.guardian_occupation,
            guardian_email: item.guardian_email,
            guardian_relationship: item.guardian_relationship,
            emergency_contact_name: item.emergency_contact_name,
            emergency_contact_phone: item.emergency_contact_phone,
            emergency_contact_country_code: item.emergency_contact_country_code,
            emergency_contact_relationship: item.emergency_contact_relationship,
            previous_school: item.previous_school,
            class_level: item.class_level,
            guardian_agreed_to_terms: item.guardian_agreed_to_terms,
            transcript_reportcard: item.transcript_reportcard,
            health_condition: item.health_condition,
            doctors_name: item.doctors_name,
            doctors_phone: item.doctors_phone,
            doctor_country_code: item.doctor_country_code,
            registered: item.registered,
            selectedFees: item.selectedFees,
            selectedResources: item.selectedResources,
            paymentMode: item.paymentMode,
            installments: item.installments,
            installmentDates: item.installmentDates.map((date: string) => new Date(date)),
            applyScholarship: item.applyScholarship,
            scholarshipAmount: item.scholarshipAmount,
            scholarshipPercentage: item.scholarshipPercentage,
            createdAt: item.createdAt,
            updatedAt: item.updatedAt,
        })) as RegistrationDraftSchema[];
    } catch (error) {
        console.error("Error fetching registration drafts:", error);

        if (error instanceof TypeError && error.message.includes('fetch')) {
            console.error("Network error - API might be unreachable");
            return [];
        }

        console.error("Registration draft fetch error:", error);
        return [];
    }
}

// Create a new registration draft
export async function createRegistrationDraft(schoolId: string, draftData: RegistrationDraftSchema): Promise<RegistrationDraftSchema | null> {
    try {
        const token = getTokenFromCookie("idToken");

        if (!token) {
            console.warn("No authentication token found");
            return null;
        }

        const response = await fetch(`${BASE_API_URL}/registration-draft/create/${schoolId}`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify(draftData),
        });

        if (!response.ok) {
            console.error("API Response Error:", {
                status: response.status,
                statusText: response.statusText,
                url: response.url,
            });

            if (response.status === 401) {
                console.warn("Unauthorized access - user may need to log in");
                return null;
            }

            throw new Error(`Failed to create registration draft: ${response.status} ${response.statusText}`);
        }

        const newDraft = await response.json();
        return newDraft as RegistrationDraftSchema;
    } catch (error) {
        console.error("Error creating registration draft:", error);
        return null;
    }
}

// Update an existing registration draft
export async function updateRegistrationDraft(schoolId: string, draftId: string, updatedData: Partial<RegistrationDraftSchema>): Promise<RegistrationDraftSchema | null> {
    try {
        const token = getTokenFromCookie("idToken");

        if (!token) {
            console.warn("No authentication token found");
            return null;
        }

        const response = await fetch(`${BASE_API_URL}/registration-draft/update/${schoolId}/${draftId}`, {
            method: "PUT",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify(updatedData),
        });

        if (!response.ok) {
            console.error("API Response Error:", {
                status: response.status,
                statusText: response.statusText,
                url: response.url,
            });

            if (response.status === 401) {
                console.warn("Unauthorized access - user may need to log in");
                return null;
            }

            throw new Error(`Failed to update registration draft: ${response.status} ${response.statusText}`);
        }

        const updatedDraft = await response.json();
        return updatedDraft as RegistrationDraftSchema;
    } catch (error) {
        console.error("Error updating registration draft:", error);
        return null;
    }
}

// Delete a registration draft
export async function deleteRegistrationDraft(schoolId: string, draftId: string): Promise<boolean> {
    try {
        const token = getTokenFromCookie("idToken");

        if (!token) {
            console.warn("No authentication token found");
            return false;
        }

        const response = await fetch(`${BASE_API_URL}/registration-draft/delete/${schoolId}/${draftId}`, {
            method: "DELETE",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
            },
        });

        if (!response.ok) {
            console.error("API Response Error:", {
                status: response.status,
                statusText: response.statusText,
                url: response.url,
            });

            if (response.status === 401) {
                console.warn("Unauthorized access - user may need to log in");
                return false;
            }

            throw new Error(`Failed to delete registration draft: ${response.status} ${response.statusText}`);
        }

        return true;
    } catch (error) {
        console.error("Error deleting registration draft:", error);
        return false;
    }
}
