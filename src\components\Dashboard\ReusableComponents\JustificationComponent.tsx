// Assume these imports are at the top of your file
import { AttendanceJustificationUpdateSchema, PopulatedAttendanceJustification, SemiPopulatedJustification } from '@/app/models/Justification';
import { UserSchema } from '@/app/models/UserModel';
import { deleteJustification, getJustificationsBySchool, getJustificationsByStudent, reviewJustification, updateJustification } from '@/app/services/JustificationServices';
import React, { useState, useEffect } from 'react';
// import { UserSchema } from '@/app/models/UserModel'; // Already provided in the prompt
// import {
//   PopulatedAttendanceJustification,
//   AttendanceJustificationSchema,
//   AttendanceJustificationUpdateSchema,
// } from '../path/to/your/schemas'; // Adjust path as needed
// import {
//   getJustificationsBySchool,
//   getJustificationsByStudent,
//   reviewJustification,
//   updateJustification,
//   deleteJustification,
// } from '../path/to/your/api-functions'; // Adjust path as needed

// Placeholder for UserSchema if not fully defined in context
// interface UserSchema {
//   _id: string;
//   name?: string;
//   email?: string;
//   role: 'student' | 'school_admin' | 'admin';
//   school_ids?: string[];
//   student_id?: string;
// }

function JustificationComponent({ user }: { user: UserSchema }) {
    // Extract schoolId for convenience, assuming the user is associated with one school primarily
    const schoolId = user.school_ids?.[0] ?? null;

    // State to hold the list of justifications
    const [justifications, setJustifications] = useState<PopulatedAttendanceJustification[]>([]);
    // State for loading status
    const [loading, setLoading] = useState(true);
    // State for error messages
    const [error, setError] = useState<string | null>(null);
    // State for managing review/update modal visibility and selected justification
    const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
    const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false);
    const [selectedJustification, setSelectedJustification] = useState<PopulatedAttendanceJustification | null>(null);
    // State for review form inputs
    const [reviewStatus, setReviewStatus] = useState<'Pending' | 'Accepted' | 'Rejected'>('Pending');
    const [reviewComment, setReviewComment] = useState('');
    // State for update form inputs
    const [updateText, setUpdateText] = useState('');
    const [updateFileUrl, setUpdateFileUrl] = useState('');

    // Effect hook to fetch justifications when the component mounts or user changes
    useEffect(() => {
        const fetchJustifications = async () => {
            setLoading(true);
            setError(null);
            try {
                let fetchedData: PopulatedAttendanceJustification[] = [];
                fetchedData = await getJustificationsBySchool(schoolId as string);

                setJustifications(fetchedData);
            } catch (err) {
                console.error("Failed to fetch justifications:", err);
                setError("Failed to load justifications. Please try again.");
            } finally {
                setLoading(false);
            }
        };

        fetchJustifications();
    }, [user, schoolId]); // Re-run effect if user or schoolId changes

    // --- Handlers for Actions ---

    const handleOpenReviewModal = (justification: PopulatedAttendanceJustification) => {
        setSelectedJustification(justification);
        setReviewStatus(justification.status); // Pre-fill with current status
        setReviewComment(justification.review_comment || ''); // Pre-fill with current comment
        setIsReviewModalOpen(true);
    };

    const handleCloseReviewModal = () => {
        setIsReviewModalOpen(false);
        setSelectedJustification(null);
        setReviewComment('');
        setReviewStatus('Pending'); // Reset
    };

    const handleReviewSubmit = async () => {
        if (!selectedJustification) return;
        setLoading(true);
        try {
            const updatedJustification = await reviewJustification(
                selectedJustification._id,
                reviewStatus,
                reviewComment
            );
            if (updatedJustification) {
                // Update the local state to reflect the change
                setJustifications((prev) =>
                    prev.map((j) =>
                        j._id === updatedJustification._id
                            ? ({ ...j, ...updatedJustification } as unknown as PopulatedAttendanceJustification)
                            : j
                    )
                );
                handleCloseReviewModal();
            } else {
                setError("Failed to review justification.");
            }
        } catch (err) {
            console.error("Error reviewing justification:", err);
            setError("Error reviewing justification. Please try again.");
        } finally {
            setLoading(false);
        }
    };

    const handleOpenUpdateModal = (justification: PopulatedAttendanceJustification) => {
        setSelectedJustification(justification);
        setUpdateText(justification.text || '');
        setUpdateFileUrl(justification.file_url || '');
        setIsUpdateModalOpen(true);
    };

    const handleCloseUpdateModal = () => {
        setIsUpdateModalOpen(false);
        setSelectedJustification(null);
        setUpdateText('');
        setUpdateFileUrl('');
    };

    const handleUpdateSubmit = async () => {
        if (!selectedJustification) return;
        setLoading(true);
        try {
            const updateData: AttendanceJustificationUpdateSchema = {
                _id: selectedJustification._id,
                text: updateText,
                file_url: updateFileUrl,
                // Status and review_comment are handled by reviewJustification,
                // but if this update allows general status changes, include it.
                // For now, assuming this is for text/file updates.
            };
            const updatedJustification = await updateJustification(updateData);
            if (updatedJustification) {
                setJustifications((prev) =>
                    prev.map((j) => {
                        if (j._id === updatedJustification._id) {
                            return {
                                ...j,
                                ...updatedJustification,
                            } as unknown as PopulatedAttendanceJustification; // Safe only if you're sure structure matches
                        }
                        return j;
                    })
                );
                handleCloseUpdateModal();
            } else {
                setError("Failed to update justification.");
            }
        } catch (err) {
            console.error("Error updating justification:", err);
            setError("Error updating justification. Please try again.");
        } finally {
            setLoading(false);
        }
    };

    const handleDeleteJustification = async (id: string) => {
        if (!window.confirm("Are you sure you want to delete this justification?")) {
            // In a real app, use a custom modal instead of window.confirm
            return;
        }
        setLoading(true);
        try {
            const success = await deleteJustification(id);
            if (success) {
                setJustifications((prev) => prev.filter((j) => j._id !== id));
            } else {
                setError("Failed to delete justification.");
            }
        } catch (err) {
            console.error("Error deleting justification:", err);
            setError("Error deleting justification. Please try again.");
        } finally {
            setLoading(false);
        }
    };

    // --- Render Logic ---

    if (loading) {
        return <div className="p-4 text-center">Loading justifications...</div>;
    }

    if (error) {
        return <div className="p-4 text-center text-red-500">Error: {error}</div>;
    }

    return (
        <div className="p-4 bg-gray-50 min-h-screen">
            <h1 className="text-2xl font-bold mb-6 text-gray-800">Attendance Justifications</h1>

            {justifications.length === 0 ? (
                <p className="text-center text-gray-600">No justifications found.</p>
            ) : (
                <div className="overflow-x-auto bg-white shadow-md rounded-lg">
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-100">
                            <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">School</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reviewer</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Comment</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {justifications.map((justification) => (
                                <tr key={justification._id}>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {new Date(justification.attendance_id.date).toLocaleDateString()}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {justification.attendance_id.student_id.first_name} {justification.attendance_id.student_id.last_name}
                                        <br />
                                        <span className="text-xs text-gray-500">({justification.attendance_id.student_id.student_id})</span>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {justification.attendance_id.school_id.name}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {justification.justification_type}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${justification.status === 'Accepted' ? 'bg-green-100 text-green-800' :
                                            justification.status === 'Rejected' ? 'bg-red-100 text-red-800' :
                                                'bg-yellow-100 text-yellow-800'
                                            }`}>
                                            {justification.status}
                                        </span>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {justification.reviewed_by?.name || 'N/A'}
                                    </td>
                                    <td className="px-6 py-4 text-sm text-gray-900">
                                        {justification.review_comment || 'No comment'}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        {/* Actions for School Admins/Admins */}
                                        {(user.role === 'school_admin' || user.role === 'admin') && (
                                            <button
                                                onClick={() => handleOpenReviewModal(justification)}
                                                className="text-indigo-600 hover:text-indigo-900 mr-3"
                                            >
                                                Review
                                            </button>
                                        )}
                                        {justification.status === 'Pending' && (
                                            <button
                                                onClick={() => handleOpenUpdateModal(justification)}
                                                className="text-blue-600 hover:text-blue-900 mr-3"
                                            >
                                                Edit
                                            </button>
                                        )}
                                        {/* Delete action (conditional based on role and status) */}
                                        {(justification.status === 'Pending') && (
                                            <button
                                                onClick={() => handleDeleteJustification(justification._id)}
                                                className="text-red-600 hover:text-red-900"
                                            >
                                                Delete
                                            </button>
                                        )}
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            )}

            {/* Review Justification Modal */}
            {isReviewModalOpen && selectedJustification && (
                <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
                    <div className="bg-white p-8 rounded-lg shadow-xl w-1/2 max-w-lg">
                        <h2 className="text-xl font-bold mb-4">Review Justification</h2>
                        <p className="mb-2"><strong>Student:</strong> {selectedJustification.attendance_id.student_id.first_name} {selectedJustification.attendance_id.student_id.last_name}</p>
                        <p className="mb-4"><strong>Justification:</strong> {selectedJustification.text || 'No text provided'}</p>

                        <div className="mb-4">
                            <label htmlFor="status" className="block text-sm font-medium text-gray-700">Status</label>
                            <select
                                id="status"
                                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                                value={reviewStatus}
                                onChange={(e) => setReviewStatus(e.target.value as 'Pending' | 'Accepted' | 'Rejected')}
                            >
                                <option value="Pending">Pending</option>
                                <option value="Accepted">Accepted</option>
                                <option value="Rejected">Rejected</option>
                            </select>
                        </div>
                        <div className="mb-6">
                            <label htmlFor="comment" className="block text-sm font-medium text-gray-700">Review Comment</label>
                            <textarea
                                id="comment"
                                rows={3}
                                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm p-2"
                                value={reviewComment}
                                onChange={(e) => setReviewComment(e.target.value)}
                                placeholder="Add your review comments here..."
                            ></textarea>
                        </div>
                        <div className="flex justify-end space-x-3">
                            <button
                                onClick={handleCloseReviewModal}
                                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={handleReviewSubmit}
                                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            >
                                Submit Review
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {/* Update Justification Modal */}
            {isUpdateModalOpen && selectedJustification && (
                <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
                    <div className="bg-white p-8 rounded-lg shadow-xl w-1/2 max-w-lg">
                        <h2 className="text-xl font-bold mb-4">Update Justification</h2>
                        <p className="mb-2"><strong>Student:</strong> {selectedJustification.attendance_id.student_id.first_name} {selectedJustification.attendance_id.student_id.last_name}</p>
                        <p className="mb-4"><strong>Current Status:</strong> {selectedJustification.status}</p>

                        <div className="mb-4">
                            <label htmlFor="updateText" className="block text-sm font-medium text-gray-700">Justification Text</label>
                            <textarea
                                id="updateText"
                                rows={4}
                                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm p-2"
                                value={updateText}
                                onChange={(e) => setUpdateText(e.target.value)}
                                placeholder="Enter updated justification text..."
                            ></textarea>
                        </div>
                        <div className="mb-6">
                            <label htmlFor="updateFileUrl" className="block text-sm font-medium text-gray-700">File URL (if applicable)</label>
                            <input
                                type="text"
                                id="updateFileUrl"
                                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm p-2"
                                value={updateFileUrl}
                                onChange={(e) => setUpdateFileUrl(e.target.value)}
                                placeholder="Enter updated file URL..."
                            />
                        </div>
                        <div className="flex justify-end space-x-3">
                            <button
                                onClick={handleCloseUpdateModal}
                                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={handleUpdateSubmit}
                                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                            >
                                Update Justification
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}

export default JustificationComponent;
