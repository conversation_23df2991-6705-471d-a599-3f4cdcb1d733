"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  ArrowLeft,
  Search,
  Filter,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  RefreshCw,
  Phone,
  DollarSign,
  CreditCard,
  User,
  Calendar,
  ExternalLink
} from 'lucide-react';
import SuperLayout from '@/components/Dashboard/Layouts/SuperLayout';
import useAuth from '@/app/hooks/useAuth';
import { useRouter } from 'next/navigation';
import CircularLoader from '@/components/widgets/CircularLoader';
import RefundModal from './components/RefundModal';
import {
  getProblematicTransactions,
  ProblematicTransaction,
  formatCurrency
} from '@/app/services/RefundServices';
const BASE_URL = "/super-admin";

export default function RefundsPage() {
  const { user, logout } = useAuth();
  const router = useRouter();
  const [transactions, setTransactions] = useState<ProblematicTransaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedTransaction, setSelectedTransaction] = useState<ProblematicTransaction | null>(null);
  const [showRefundModal, setShowRefundModal] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const navigation = {
    icon: CreditCard,
    baseHref: `${BASE_URL}/refunds`,
    title: "Gestion des Remboursements",
  };
  const fetchData = async () => {
    try {
      setLoading(true);
      const fetchedTransactions = await getProblematicTransactions();
      setTransactions(fetchedTransactions);
    } catch (error) {
      console.error("Error fetching transactions:", error);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchData();
  }, []);

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchData();
    setRefreshing(false);
  };

  const handleRefund = (transaction: ProblematicTransaction) => {
    setSelectedTransaction(transaction);
    setShowRefundModal(true);
  };

  const handleRefundSuccess = () => {
    setShowRefundModal(false);
    setSelectedTransaction(null);
    fetchData();
  };

  const filteredTransactions = transactions.filter(transaction => {
    const matchesSearch = 
      transaction.school_id.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.transaction_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.billing_info.email?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || transaction.payment_status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'expired':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'failed':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      case 'expired':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  const getPriorityLevel = (transaction: ProblematicTransaction) => {
    const daysPending = transaction.days_pending || 0;
    if (transaction.payment_status === 'failed') return 'high';
    if (daysPending > 2) return 'high';
    if (daysPending > 1) return 'medium';
    return 'low';
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'border-l-red-500 bg-red-50 dark:bg-red-900/10';
      case 'medium':
        return 'border-l-yellow-500 bg-yellow-50 dark:bg-yellow-900/10';
      default:
        return 'border-l-blue-500 bg-blue-50 dark:bg-blue-900/10';
    }
  };

  if (loading) {
    return (
      <SuperLayout navigation={navigation} showGoPro={true} onLogout={() => logout()}>
        <div className="flex items-center justify-center h-64">
          <CircularLoader />
        </div>
      </SuperLayout>
    );
  }

  return (
    <SuperLayout navigation={navigation} showGoPro={true} onLogout={() => logout()}>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => router.push(`${BASE_URL}/credit`)}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              <ArrowLeft className="h-5 w-5 text-gray-600 dark:text-gray-400" />
            </button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Gestion des Remboursements
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Transactions problématiques nécessitant une attention
              </p>
            </div>
          </div>
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            <span>Actualiser</span>
          </button>
        </div>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Rechercher par école, transaction ID, email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            />
          </div>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
          >
            <option value="all">Tous les statuts</option>
            <option value="pending">En attente</option>
            <option value="failed">Échoué</option>
            <option value="expired">Expiré</option>
          </select>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center">
              <AlertTriangle className="h-8 w-8 text-red-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Transactions problématiques</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{transactions.length}</p>
              </div>
            </div>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-yellow-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">En attente</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {transactions.filter(t => t.payment_status === 'pending').length}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center">
              <XCircle className="h-8 w-8 text-red-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Échouées</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {transactions.filter(t => t.payment_status === 'failed').length}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center">
              <DollarSign className="h-8 w-8 text-green-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Montant total</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {transactions.reduce((sum, t) => sum + t.total_amount, 0).toLocaleString()} XAF
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Transactions List */}
        <div className="space-y-4">
          {filteredTransactions.length === 0 ? (
            <div className="text-center py-12">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Aucune transaction problématique
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Toutes les transactions sont en bon état !
              </p>
            </div>
          ) : (
            filteredTransactions.map((transaction) => {
              const priority = getPriorityLevel(transaction);
              return (
                <motion.div
                  key={transaction._id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={`border-l-4 ${getPriorityColor(priority)} bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-4 mb-4">
                        {getStatusIcon(transaction.payment_status)}
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(transaction.payment_status)}`}>
                          {transaction.payment_status.toUpperCase()}
                        </span>
                        {priority === 'high' && (
                          <span className="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400">
                            PRIORITÉ HAUTE
                          </span>
                        )}
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">École</p>
                          <p className="text-lg font-semibold text-gray-900 dark:text-white">
                            {transaction.school_id.name}
                          </p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            ID: {transaction.transaction_id}
                          </p>
                        </div>
                        
                        <div>
                          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Montant & Crédits</p>
                          <p className="text-lg font-semibold text-gray-900 dark:text-white">
                            {transaction.total_amount.toLocaleString()} XAF
                          </p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {transaction.credits_purchased} crédits
                          </p>
                        </div>
                        
                        <div>
                          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Contact</p>
                          <p className="text-sm text-gray-900 dark:text-white">
                            {transaction.billing_info.name || transaction.school_id.name}
                          </p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {transaction.billing_info.phone || transaction.school_id.phone || 'N/A'}
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex flex-col space-y-2 ml-6">
                      <button
                        onClick={() => handleRefund(transaction)}
                        className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2"
                      >
                        <DollarSign className="h-4 w-4" />
                        <span>Rembourser</span>
                      </button>
                      <button
                        onClick={() => window.open(`/super-admin/credit/manage?id=${transaction.school_id._id}`, '_blank')}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                      >
                        <ExternalLink className="h-4 w-4" />
                        <span>Détails</span>
                      </button>
                    </div>
                  </div>
                </motion.div>
              );
            })
          )}
        </div>
      </div>

      {/* Refund Modal */}
      {showRefundModal && selectedTransaction && (
        <RefundModal
          transaction={selectedTransaction}
          onClose={() => setShowRefundModal(false)}
          onSuccess={handleRefundSuccess}
        />
      )}
    </SuperLayout>
  );
}
