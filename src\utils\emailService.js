const nodemailer = require('nodemailer');


// Configuration du transporteur email
const createTransporter = async () => {
  if (process.env.EMAIL_USER && process.env.EMAIL_PASS) {
    return nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });
  }

  // Fallback pour le développement (utilise Ethereal)
  const testAccount = await nodemailer.createTestAccount();
  return nodemailer.createTransport({
    host: 'smtp.ethereal.email',
    port: 587,
    secure: false,
    auth: {
      user: testAccount.user,
      pass: testAccount.pass,
    },
  });
};

/**
 * Templates d'email
 */
const emailTemplates = {
  'payment-confirmation': (data) => ({
    subject: data.subject || '✅ Confirmation d\'achat de crédits - Scholarify',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0;">Scholarify</h1>
          <p style="color: white; margin: 10px 0 0 0;">Système de gestion scolaire</p>
        </div>
        
        <div style="padding: 30px; background: #f8f9fa;">
          <h2 style="color: #333; margin-bottom: 20px;">🎉 Paiement confirmé !</h2>
          
          <p>Bonjour <strong>${data.userName}</strong>,</p>
          
          <p>Nous avons le plaisir de vous confirmer que votre achat de crédits a été traité avec succès.</p>
          
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;">
            <h3 style="margin-top: 0; color: #333;">Détails de l'achat</h3>
            <ul style="list-style: none; padding: 0;">
              <li style="margin: 10px 0;"><strong>École :</strong> ${data.schoolName}</li>
              <li style="margin: 10px 0;"><strong>Crédits achetés :</strong> ${data.creditsAmount}</li>
              <li style="margin: 10px 0;"><strong>Montant payé :</strong> ${data.totalAmount} ${data.currency}</li>
              <li style="margin: 10px 0;"><strong>Transaction ID :</strong> ${data.transactionId}</li>
              <li style="margin: 10px 0;"><strong>Date :</strong> ${data.purchaseDate}</li>
            </ul>
          </div>
          
          <p>Les crédits ont été automatiquement ajoutés à votre compte et sont maintenant disponibles pour utilisation.</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${data.dashboardUrl}" style="background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Accéder au tableau de bord
            </a>
          </div>
          
          <p style="color: #666; font-size: 14px; margin-top: 30px;">
            Si vous avez des questions, n'hésitez pas à nous contacter.<br>
            Merci de faire confiance à Scholarify !
          </p>
        </div>
        
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p>© 2024 Scholarify. Tous droits réservés.</p>
        </div>
      </div>
    `,
    text: `
      Bonjour ${data.userName},
      
      Votre achat de crédits a été confirmé !
      
      Détails :
      - École : ${data.schoolName}
      - Crédits : ${data.creditsAmount}
      - Montant : ${data.totalAmount} ${data.currency}
      - Transaction : ${data.transactionId}
      - Date : ${data.purchaseDate}
      
      Accédez à votre tableau de bord : ${data.dashboardUrl}
      
      Merci de faire confiance à Scholarify !
    `
  }),

  'payment-failure': (data) => ({
    subject: data.subject || '❌ Échec de paiement - Scholarify',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0;">Scholarify</h1>
          <p style="color: white; margin: 10px 0 0 0;">Système de gestion scolaire</p>
        </div>
        
        <div style="padding: 30px; background: #f8f9fa;">
          <h2 style="color: #dc3545; margin-bottom: 20px;">❌ Paiement non abouti</h2>
          
          <p>Bonjour <strong>${data.userName}</strong>,</p>
          
          <p>Nous vous informons que votre tentative de paiement n'a pas pu être traitée.</p>
          
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc3545;">
            <h3 style="margin-top: 0; color: #333;">Détails de la tentative</h3>
            <ul style="list-style: none; padding: 0;">
              <li style="margin: 10px 0;"><strong>École :</strong> ${data.schoolName}</li>
              <li style="margin: 10px 0;"><strong>Crédits :</strong> ${data.creditsAmount}</li>
              <li style="margin: 10px 0;"><strong>Montant :</strong> ${data.totalAmount} ${data.currency}</li>
              <li style="margin: 10px 0;"><strong>Transaction ID :</strong> ${data.transactionId}</li>
            </ul>
          </div>
          
          <p>Vous pouvez réessayer votre achat en cliquant sur le bouton ci-dessous.</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${data.retryUrl}" style="background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Réessayer l'achat
            </a>
          </div>
          
          <p style="color: #666; font-size: 14px; margin-top: 30px;">
            Si le problème persiste, veuillez nous contacter pour assistance.
          </p>
        </div>
        
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p>© 2024 Scholarify. Tous droits réservés.</p>
        </div>
      </div>
    `,
    text: `
      Bonjour ${data.userName},
      
      Votre paiement n'a pas pu être traité.
      
      Détails :
      - École : ${data.schoolName}
      - Crédits : ${data.creditsAmount}
      - Montant : ${data.totalAmount} ${data.currency}
      - Transaction : ${data.transactionId}
      
      Réessayez ici : ${data.retryUrl}
      
      Contactez-nous si le problème persiste.
    `
  }),

  'payment-expiration': (data) => ({
    subject: data.subject || '⏰ Lien de paiement expiré - Scholarify',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0;">Scholarify</h1>
          <p style="color: white; margin: 10px 0 0 0;">Système de gestion scolaire</p>
        </div>
        
        <div style="padding: 30px; background: #f8f9fa;">
          <h2 style="color: #ffc107; margin-bottom: 20px;">⏰ Lien de paiement expiré</h2>
          
          <p>Bonjour <strong>${data.userName}</strong>,</p>
          
          <p>Le lien de paiement pour votre achat de crédits a expiré après 24 heures.</p>
          
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ffc107;">
            <h3 style="margin-top: 0; color: #333;">Détails de l'achat</h3>
            <ul style="list-style: none; padding: 0;">
              <li style="margin: 10px 0;"><strong>École :</strong> ${data.schoolName}</li>
              <li style="margin: 10px 0;"><strong>Crédits :</strong> ${data.creditsAmount}</li>
              <li style="margin: 10px 0;"><strong>Montant :</strong> ${data.totalAmount} ${data.currency}</li>
              <li style="margin: 10px 0;"><strong>Transaction ID :</strong> ${data.transactionId}</li>
            </ul>
          </div>
          
          <p>Vous pouvez créer un nouveau lien de paiement en cliquant ci-dessous.</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${data.newPurchaseUrl}" style="background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Nouvel achat
            </a>
          </div>
          
          <p style="color: #666; font-size: 14px; margin-top: 30px;">
            Les liens de paiement expirent automatiquement après 24 heures pour votre sécurité.
          </p>
        </div>
        
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p>© 2024 Scholarify. Tous droits réservés.</p>
        </div>
      </div>
    `,
    text: `
      Bonjour ${data.userName},
      
      Votre lien de paiement a expiré.
      
      Détails :
      - École : ${data.schoolName}
      - Crédits : ${data.creditsAmount}
      - Montant : ${data.totalAmount} ${data.currency}
      - Transaction : ${data.transactionId}
      
      Créez un nouveau lien : ${data.newPurchaseUrl}
      
      Les liens expirent après 24h pour votre sécurité.
    `
  })
};

/**
 * Envoyer un email
 */
const sendEmail = async ({ to, subject, template, data, html, text }) => {
  try {
    const transporter = await createTransporter();

    let emailContent = {};

    if (template && emailTemplates[template]) {
      emailContent = emailTemplates[template](data);
    } else {
      emailContent = { subject, html, text };
    }

    const mailOptions = {
      from: process.env.EMAIL_FROM || process.env.EMAIL_USER || '<EMAIL>',
      to,
      subject: emailContent.subject,
      html: emailContent.html,
      text: emailContent.text,
    };

    const info = await transporter.sendMail(mailOptions);

    console.log('📧 Email envoyé:', {
      to,
      subject: emailContent.subject,
      messageId: info.messageId
    });

    // Log pour le développement
    if (process.env.NODE_ENV !== 'production') {
      console.log('Preview URL:', nodemailer.getTestMessageUrl(info));
    }

    return info;

  } catch (error) {
    console.error('❌ Erreur envoi email:', error);
    throw error;
  }
};

/**
 * Envoyer un email simple
 */
const sendSimpleEmail = async (to, subject, message) => {
  return sendEmail({
    to,
    subject,
    html: `<p>${message}</p>`,
    text: message
  });
};

module.exports = {
  sendEmail,
  sendSimpleEmail,
  emailTemplates
};
