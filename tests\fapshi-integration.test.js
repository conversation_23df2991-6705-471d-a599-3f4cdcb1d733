const request = require('supertest');
const app = require('../src/server'); // Assuming server exports the app
const fapshi = require('../src/utils/fapshi');

// Mock Fapshi API responses
jest.mock('../src/utils/fapshi');

describe('Fapshi Integration Tests', () => {
  let authToken;
  let schoolId;

  beforeAll(async () => {
    // Setup test data - you'll need to implement this based on your auth system
    // authToken = await getTestAuthToken();
    // schoolId = await createTestSchool();
  });

  afterAll(async () => {
    // Cleanup test data
  });

  describe('POST /api/fapshi/initiate-payment', () => {
    it('should initiate payment successfully', async () => {
      const mockResponse = {
        statusCode: 200,
        link: 'https://sandbox.fapshi.com/pay/test123',
        transId: 'TEST123',
        message: 'Payment initiated successfully'
      };

      fapshi.initiatePay.mockResolvedValue(mockResponse);

      const paymentData = {
        userId: 'test-user-id',
        amount: 5000,
        email: '<EMAIL>',
        externalId: 'test-order-123',
        redirectUrl: 'http://localhost:3000/success'
      };

      const response = await request(app)
        .post('/api/fapshi/initiate-payment')
        .set('Authorization', `Bearer ${authToken}`)
        .send(paymentData)
        .expect(200);

      expect(response.body.link).toBeDefined();
      expect(response.body.transId).toBeDefined();
    });

    it('should reject payment with invalid amount', async () => {
      const paymentData = {
        userId: 'test-user-id',
        amount: 50, // Less than minimum 100 XAF
        email: '<EMAIL>'
      };

      const response = await request(app)
        .post('/api/fapshi/initiate-payment')
        .set('Authorization', `Bearer ${authToken}`)
        .send(paymentData)
        .expect(400);

      expect(response.body.message).toContain('amount');
    });
  });

  describe('GET /api/fapshi/payment-status/:transId', () => {
    it('should get payment status successfully', async () => {
      const mockStatus = {
        statusCode: 200,
        transId: 'TEST123',
        status: 'SUCCESSFUL',
        amount: 5000,
        email: '<EMAIL>'
      };

      fapshi.paymentStatus.mockResolvedValue(mockStatus);

      const response = await request(app)
        .get('/api/fapshi/payment-status/TEST123')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.status).toBe('SUCCESSFUL');
      expect(response.body.transId).toBe('TEST123');
    });

    it('should reject invalid transaction ID', async () => {
      const response = await request(app)
        .get('/api/fapshi/payment-status/invalid-id')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response.body.message).toContain('transaction id');
    });
  });

  describe('GET /api/fapshi/search-transactions', () => {
    it('should search transactions successfully (admin only)', async () => {
      const mockResults = {
        statusCode: 200,
        data: [
          {
            transId: 'TEST123',
            status: 'SUCCESSFUL',
            amount: 5000
          }
        ]
      };

      fapshi.searchTransactions.mockResolvedValue(mockResults);

      const response = await request(app)
        .get('/api/fapshi/search-transactions?status=successful&limit=10')
        .set('Authorization', `Bearer ${authToken}`) // Admin token
        .expect(200);

      expect(Array.isArray(response.body.data)).toBe(true);
    });

    it('should reject non-admin users', async () => {
      const response = await request(app)
        .get('/api/fapshi/search-transactions')
        .set('Authorization', `Bearer ${authToken}`) // Non-admin token
        .expect(403);

      expect(response.body.message).toContain('Admin access required');
    });
  });

  describe('GET /api/fapshi/service-balance', () => {
    it('should get service balance successfully (admin only)', async () => {
      const mockBalance = {
        statusCode: 200,
        service: 'fapshi',
        balance: 50000,
        currency: 'XAF'
      };

      fapshi.getServiceBalance.mockResolvedValue(mockBalance);

      const response = await request(app)
        .get('/api/fapshi/service-balance')
        .set('Authorization', `Bearer ${authToken}`) // Admin token
        .expect(200);

      expect(response.body.balance).toBeDefined();
      expect(response.body.currency).toBe('XAF');
    });
  });

  describe('POST /api/fapshi/expire-transaction/:transId', () => {
    it('should expire transaction successfully', async () => {
      const mockResponse = {
        statusCode: 200,
        transId: 'TEST123',
        status: 'EXPIRED',
        message: 'Transaction expired successfully'
      };

      fapshi.expirePay.mockResolvedValue(mockResponse);

      const response = await request(app)
        .post('/api/fapshi/expire-transaction/TEST123')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.status).toBe('EXPIRED');
    });
  });

  describe('POST /api/fapshi/webhook', () => {
    it('should process valid webhook successfully', async () => {
      const mockValidation = {
        valid: true,
        transaction: {
          transId: 'TEST123',
          status: 'SUCCESSFUL',
          amount: 5000,
          email: '<EMAIL>'
        }
      };

      fapshi.validateWebhook.mockResolvedValue(mockValidation);

      const webhookData = {
        transId: 'TEST123',
        status: 'SUCCESSFUL',
        amount: 5000,
        email: '<EMAIL>'
      };

      const response = await request(app)
        .post('/api/fapshi/webhook')
        .send(webhookData)
        .expect(200);

      expect(response.body.message).toContain('processed successfully');
    });

    it('should reject invalid webhook', async () => {
      const mockValidation = {
        valid: false,
        message: 'Invalid webhook signature'
      };

      fapshi.validateWebhook.mockResolvedValue(mockValidation);

      const webhookData = {
        transId: 'INVALID123',
        status: 'SUCCESSFUL',
        amount: 5000
      };

      const response = await request(app)
        .post('/api/fapshi/webhook')
        .send(webhookData)
        .expect(400);

      expect(response.body.message).toContain('Invalid webhook');
    });
  });

  describe('POST /api/fapshi/webhook/test', () => {
    it('should process test webhook in development', async () => {
      // Only run this test in development environment
      if (process.env.NODE_ENV === 'production') {
        return;
      }

      const testData = {
        transId: 'TEST123',
        status: 'SUCCESSFUL'
      };

      const response = await request(app)
        .post('/api/fapshi/webhook/test')
        .set('Authorization', `Bearer ${authToken}`) // Admin token
        .send(testData)
        .expect(200);

      expect(response.body.message).toContain('processed');
    });

    it('should reject test webhook in production', async () => {
      // Mock production environment
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      const response = await request(app)
        .post('/api/fapshi/webhook/test')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ transId: 'TEST123' })
        .expect(403);

      expect(response.body.message).toContain('not available in production');

      // Restore environment
      process.env.NODE_ENV = originalEnv;
    });
  });
});

// Helper functions for testing
async function getTestAuthToken() {
  // Implement based on your auth system
  // This should return a valid JWT token for testing
  return 'test-jwt-token';
}

async function createTestSchool() {
  // Implement based on your school model
  // This should create a test school and return its ID
  return 'test-school-id';
}

// Integration test for the complete payment flow
describe('Complete Payment Flow Integration', () => {
  it('should handle complete payment flow from initiation to webhook', async () => {
    // 1. Initiate payment
    const mockInitResponse = {
      statusCode: 200,
      link: 'https://sandbox.fapshi.com/pay/test123',
      transId: 'FLOW_TEST_123'
    };
    fapshi.initiatePay.mockResolvedValue(mockInitResponse);

    const paymentData = {
      userId: 'test-user-id',
      amount: 3000,
      email: '<EMAIL>'
    };

    const initResponse = await request(app)
      .post('/api/fapshi/initiate-payment')
      .set('Authorization', `Bearer ${authToken}`)
      .send(paymentData)
      .expect(200);

    const transId = initResponse.body.transId;

    // 2. Check initial status (should be CREATED)
    const mockStatusCreated = {
      statusCode: 200,
      transId,
      status: 'CREATED',
      amount: 3000
    };
    fapshi.paymentStatus.mockResolvedValue(mockStatusCreated);

    await request(app)
      .get(`/api/fapshi/payment-status/${transId}`)
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    // 3. Simulate webhook for successful payment
    const mockWebhookValidation = {
      valid: true,
      transaction: {
        transId,
        status: 'SUCCESSFUL',
        amount: 3000,
        email: '<EMAIL>'
      }
    };
    fapshi.validateWebhook.mockResolvedValue(mockWebhookValidation);

    await request(app)
      .post('/api/fapshi/webhook')
      .send({
        transId,
        status: 'SUCCESSFUL',
        amount: 3000,
        email: '<EMAIL>'
      })
      .expect(200);

    // 4. Check final status (should be SUCCESSFUL)
    const mockStatusSuccess = {
      statusCode: 200,
      transId,
      status: 'SUCCESSFUL',
      amount: 3000
    };
    fapshi.paymentStatus.mockResolvedValue(mockStatusSuccess);

    const finalResponse = await request(app)
      .get(`/api/fapshi/payment-status/${transId}`)
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    expect(finalResponse.body.status).toBe('SUCCESSFUL');
  });
});
