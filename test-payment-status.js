const mongoose = require('mongoose');
const CreditPurchase = require('./src/models/CreditPurchase');

// Configuration de la base de données
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/scholarify';

async function connectDB() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connexion à MongoDB réussie');
  } catch (error) {
    console.error('❌ Erreur de connexion à MongoDB:', error);
    process.exit(1);
  }
}

async function testPaymentStatusLookup() {
  console.log('\n🧪 === TEST DE RECHERCHE DE STATUT DE PAIEMENT ===\n');

  try {
    // 1. Trouver tous les achats de crédits
    const purchases = await CreditPurchase.find()
      .populate('school_id', 'name')
      .populate('purchased_by', 'name email')
      .sort({ purchase_date: -1 })
      .limit(10);

    console.log(`📋 Achats de crédits trouvés: ${purchases.length}`);

    purchases.forEach((purchase, index) => {
      console.log(`\n${index + 1}. Achat: ${purchase.purchase_id}`);
      console.log(`   - Transaction ID (Fapshi transId): ${purchase.transaction_id}`);
      console.log(`   - Statut: ${purchase.payment_status}`);
      console.log(`   - École: ${purchase.school_id?.name || 'N/A'}`);
      console.log(`   - Crédits: ${purchase.credits_purchased}`);
      console.log(`   - Montant: ${purchase.total_amount} ${purchase.currency}`);
      console.log(`   - Date: ${purchase.purchase_date}`);

      // Vérifier la cohérence entre transaction_id et transId dans payment_gateway_response
      const gatewayTransId = purchase.payment_gateway_response?.transId;
      const isConsistent = purchase.transaction_id === gatewayTransId;

      console.log(`   - Cohérence ID: ${isConsistent ? '✅' : '❌'} (${purchase.transaction_id} vs ${gatewayTransId})`);

      if (purchase.payment_gateway_response) {
        console.log(`   - Réponse gateway:`, {
          transId: purchase.payment_gateway_response.transId,
          link: purchase.payment_gateway_response.link ? 'Présent' : 'Absent',
          statusCode: purchase.payment_gateway_response.statusCode
        });
      }
    });

    // 2. Tester la recherche par transaction_id (maintenant le transId de Fapshi)
    if (purchases.length > 0) {
      const testPurchase = purchases[0];

      console.log(`\n🔍 Test de recherche pour: ${testPurchase.purchase_id}`);
      console.log(`   Transaction ID: ${testPurchase.transaction_id}`);

      // Test: Recherche par transaction_id (qui est maintenant le transId de Fapshi)
      const foundById = await CreditPurchase.findOne({
        transaction_id: testPurchase.transaction_id
      });
      console.log(`✅ Trouvé par transaction_id: ${foundById ? 'OUI' : 'NON'}`);

      // Vérifier la cohérence
      const gatewayTransId = testPurchase.payment_gateway_response?.transId;
      if (gatewayTransId && testPurchase.transaction_id !== gatewayTransId) {
        console.log(`⚠️ INCOHÉRENCE: transaction_id (${testPurchase.transaction_id}) != gateway transId (${gatewayTransId})`);
      } else {
        console.log(`✅ Cohérence des IDs confirmée`);
      }
    }

    // 3. Identifier les problèmes potentiels
    console.log(`\n🔍 === ANALYSE DES PROBLÈMES POTENTIELS ===`);
    
    const pendingPurchases = purchases.filter(p => p.payment_status === 'pending');
    console.log(`⏳ Achats en attente: ${pendingPurchases.length}`);
    
    const inconsistentIds = purchases.filter(p =>
      p.payment_method === 'fapshi' &&
      p.payment_gateway_response?.transId &&
      p.transaction_id !== p.payment_gateway_response.transId
    );
    console.log(`⚠️ Achats avec IDs incohérents: ${inconsistentIds.length}`);

    const purchasesWithoutFapshiId = purchases.filter(p =>
      p.payment_method === 'fapshi' && !p.payment_gateway_response?.transId
    );
    console.log(`⚠️ Achats Fapshi sans transId: ${purchasesWithoutFapshiId.length}`);

    const completedPurchases = purchases.filter(p => p.payment_status === 'completed');
    console.log(`✅ Achats complétés: ${completedPurchases.length}`);

    const failedPurchases = purchases.filter(p => p.payment_status === 'failed');
    console.log(`❌ Achats échoués: ${failedPurchases.length}`);

    // 4. Afficher les achats problématiques
    if (inconsistentIds.length > 0) {
      console.log(`\n⚠️ Achats avec IDs incohérents (anciens achats):`);
      inconsistentIds.forEach((purchase, index) => {
        console.log(`   ${index + 1}. ${purchase.purchase_id}`);
        console.log(`      - transaction_id: ${purchase.transaction_id}`);
        console.log(`      - gateway transId: ${purchase.payment_gateway_response.transId}`);
        console.log(`      - statut: ${purchase.payment_status}`);
      });
    }

    if (purchasesWithoutFapshiId.length > 0) {
      console.log(`\n⚠️ Achats Fapshi sans transId (problématiques):`);
      purchasesWithoutFapshiId.forEach((purchase, index) => {
        console.log(`   ${index + 1}. ${purchase.purchase_id} - ${purchase.transaction_id} - ${purchase.payment_status}`);
      });
    }

  } catch (error) {
    console.error('❌ Erreur lors du test:', error);
  }
}

async function simulatePaymentStatusCheck(transactionId) {
  console.log(`\n🧪 === SIMULATION DE VÉRIFICATION DE STATUT ===`);
  console.log(`🔍 Recherche pour transaction_id: ${transactionId}`);

  try {
    // Simuler la nouvelle logique du contrôleur (transaction_id = transId Fapshi)
    const purchase = await CreditPurchase.findOne({ transaction_id: transactionId });

    if (!purchase) {
      console.log(`❌ Transaction non trouvée pour ID: ${transactionId}`);
      return null;
    }

    console.log(`✅ Transaction trouvée:`, {
      purchase_id: purchase.purchase_id,
      transaction_id: purchase.transaction_id,
      payment_status: purchase.payment_status,
      payment_method: purchase.payment_method
    });

    // Vérifier la cohérence
    const gatewayTransId = purchase.payment_gateway_response?.transId;
    if (gatewayTransId && purchase.transaction_id !== gatewayTransId) {
      console.log(`⚠️ ATTENTION: Ancien achat avec IDs incohérents`);
      console.log(`   - transaction_id: ${purchase.transaction_id}`);
      console.log(`   - gateway transId: ${gatewayTransId}`);
    }

    return purchase;
  } catch (error) {
    console.error('❌ Erreur lors de la simulation:', error);
    return null;
  }
}

async function main() {
  await connectDB();
  
  await testPaymentStatusLookup();
  
  // Test avec un ID spécifique si fourni en argument
  const testId = process.argv[2];
  if (testId) {
    await simulatePaymentStatusCheck(testId);
  }
  
  console.log('\n✅ Tests terminés');
  process.exit(0);
}

// Exécuter le script
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Erreur fatale:', error);
    process.exit(1);
  });
}

module.exports = {
  testPaymentStatusLookup,
  simulatePaymentStatusCheck
};
