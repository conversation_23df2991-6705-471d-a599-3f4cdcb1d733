"use client";

import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>hart, 
  Pie, 
  Cell, 
  ResponsiveContainer, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend 
} from 'recharts';
import {
  Crown,
  Users,
  TrendingUp,
  DollarSign,
  Calendar,
  Award,
  Target
} from 'lucide-react';
import { motion } from 'framer-motion';
import { getSubscriptionDistribution, SubscriptionDistributionResponse } from '@/app/services/SubscriptionServices';

interface SubscriptionData {
  plan_type: string;
  count: number;
  revenue: number;
  color: string;
}

interface MonthlyData {
  month: string;
  basic: number;
  premium: number;
  enterprise: number;
  revenue: number;
}

interface SubscriptionOverviewProps {
  schoolId: string;
  className?: string;
}

const SubscriptionOverview: React.FC<SubscriptionOverviewProps> = ({ 
  schoolId, 
  className = '' 
}) => {
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData[]>([]);
  const [monthlyData, setMonthlyData] = useState<MonthlyData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeView, setActiveView] = useState<'distribution' | 'trends'>('distribution');

  // Données de démonstration
  const generateDemoData = () => {
    const subscriptions: SubscriptionData[] = [
      { plan_type: 'Basic', count: 45, revenue: 135000, color: '#3B82F6' },
      { plan_type: 'Premium', count: 28, revenue: 280000, color: '#10B981' },
      { plan_type: 'Enterprise', count: 12, revenue: 360000, color: '#F59E0B' }
    ];

    const monthly: MonthlyData[] = [
      { month: 'Jan', basic: 40, premium: 25, enterprise: 10, revenue: 750000 },
      { month: 'Fév', basic: 42, premium: 26, enterprise: 11, revenue: 790000 },
      { month: 'Mar', basic: 45, premium: 28, enterprise: 12, revenue: 850000 },
      { month: 'Avr', basic: 43, premium: 30, enterprise: 13, revenue: 860000 },
      { month: 'Mai', basic: 47, premium: 32, enterprise: 14, revenue: 930000 },
      { month: 'Jun', basic: 45, premium: 28, enterprise: 12, revenue: 775000 }
    ];

    return { subscriptions, monthly };
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Utiliser les vraies données de l'API avec schoolId
        const response = await getSubscriptionDistribution(schoolId);
        setSubscriptionData(response.distribution);
        setMonthlyData(response.monthly_trends);

      } catch (err: any) {
        console.error('Error fetching subscription distribution:', err);
        // Fallback vers les données de démonstration en cas d'erreur
        const { subscriptions, monthly } = generateDemoData();
        setSubscriptionData(subscriptions);
        setMonthlyData(monthly);
        setError(err.message || 'Erreur lors du chargement des données');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [schoolId]); // Dépendre de schoolId pour recharger les données

  const totalSubscriptions = subscriptionData.reduce((sum, item) => sum + item.count, 0);
  const totalRevenue = subscriptionData.reduce((sum, item) => sum + item.revenue, 0);
  const averageRevenue = totalSubscriptions > 0 ? totalRevenue / totalSubscriptions : 0;

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 dark:text-white">{data.plan_type || data.month}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {entry.value}
              {entry.dataKey === 'revenue' && ' FCFA'}
              {entry.dataKey === 'count' && ' écoles'}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  const PieTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      const percentage = ((data.value / totalSubscriptions) * 100).toFixed(1);
      return (
        <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 dark:text-white">{data.payload.plan_type}</p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {data.value} écoles ({percentage}%)
          </p>
          <p className="text-sm text-green-600">
            {data.payload.revenue.toLocaleString()} FCFA
          </p>
        </div>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-4"></div>
          <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 ${className}`}>
        <div className="text-center py-8">
          <p className="text-red-600 dark:text-red-400">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 ${className}`}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
            <Crown className="h-5 w-5 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Vue d'ensemble des Souscriptions
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Distribution et tendances des plans
            </p>
          </div>
        </div>

        {/* View Toggle */}
        <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
          <button
            onClick={() => setActiveView('distribution')}
            className={`px-3 py-1 text-sm rounded-md transition-colors ${
              activeView === 'distribution'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-400'
            }`}
          >
            Distribution
          </button>
          <button
            onClick={() => setActiveView('trends')}
            className={`px-3 py-1 text-sm rounded-md transition-colors ${
              activeView === 'trends'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-400'
            }`}
          >
            Tendances
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <Users className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            <div>
              <p className="text-sm text-blue-600 dark:text-blue-400 font-medium">Total Écoles</p>
              <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">
                {totalSubscriptions}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <DollarSign className="h-8 w-8 text-green-600 dark:text-green-400" />
            <div>
              <p className="text-sm text-green-600 dark:text-green-400 font-medium">Revenus Total</p>
              <p className="text-2xl font-bold text-green-700 dark:text-green-300">
                {(totalRevenue / 1000).toFixed(0)}K
              </p>
            </div>
          </div>
        </div>

        <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <Target className="h-8 w-8 text-orange-600 dark:text-orange-400" />
            <div>
              <p className="text-sm text-orange-600 dark:text-orange-400 font-medium">ARPU</p>
              <p className="text-2xl font-bold text-orange-700 dark:text-orange-300">
                {(averageRevenue / 1000).toFixed(0)}K
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="h-80 w-full">
        {activeView === 'distribution' ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full">
            {/* Pie Chart */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Répartition par Plan
              </h4>
              <ResponsiveContainer width="100%" height={280}>
                <PieChart>
                  <Pie
                    data={subscriptionData}
                    cx="50%"
                    cy="50%"
                    innerRadius={40}
                    outerRadius={80}
                    paddingAngle={5}
                    dataKey="count"
                  >
                    {subscriptionData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip content={<PieTooltip />} />
                </PieChart>
              </ResponsiveContainer>
            </div>

            {/* Plan Details */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Détails des Plans
              </h4>
              <div className="space-y-3">
                {subscriptionData.map((plan, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div 
                        className="w-4 h-4 rounded-full"
                        style={{ backgroundColor: plan.color }}
                      ></div>
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">
                          {plan.plan_type}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {plan.count} écoles
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-gray-900 dark:text-white">
                        {(plan.revenue / 1000).toFixed(0)}K FCFA
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {((plan.count / totalSubscriptions) * 100).toFixed(1)}%
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ) : (
          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Évolution Mensuelle
            </h4>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={monthlyData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                <XAxis 
                  dataKey="month" 
                  axisLine={false}
                  tick={{ fontSize: 12, fill: '#6B7280' }}
                />
                <YAxis 
                  axisLine={false}
                  tick={{ fontSize: 12, fill: '#6B7280' }}
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Bar dataKey="basic" stackId="a" fill="#3B82F6" name="Basic" />
                <Bar dataKey="premium" stackId="a" fill="#10B981" name="Premium" />
                <Bar dataKey="enterprise" stackId="a" fill="#F59E0B" name="Enterprise" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default SubscriptionOverview;
