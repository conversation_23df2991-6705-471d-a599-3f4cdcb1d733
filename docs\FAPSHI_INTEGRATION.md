# Documentation Intégration Fapshi

## Vue d'ensemble

Cette documentation décrit l'intégration complète avec l'API Fapshi pour les paiements mobiles au Cameroun. L'utilitaire `src/utils/fapshi.js` implémente toutes les fonctionnalités disponibles dans l'API Fapshi.

## Configuration

### Variables d'environnement

Ajoutez ces variables à votre fichier `.env` :

```env
FAPSHI_BASE_URL=https://sandbox.fapshi.com  # ou https://api.fapshi.com pour la production
FAPSHI_API_USER=votre-api-user
FAPSHI_API_KEY=votre-api-key
```

## Fonctionnalités disponibles

### 1. Initiation de paiement (`initiatePay`)

Crée un lien de paiement pour rediriger l'utilisateur.

```javascript
const fapshi = require('../utils/fapshi');

const paymentData = {
    amount: 1000,                    // Requis: montant en XAF (minimum 100)
    email: "<EMAIL>",       // Optionnel: email du payeur
    userId: "user123",               // Optionnel: ID utilisateur
    externalId: "order456",          // Optionnel: ID externe pour référence
    redirectUrl: "https://...",      // Optionnel: URL de redirection après paiement
    message: "Achat de crédits"      // Optionnel: message descriptif
};

const result = await fapshi.initiatePay(paymentData);
if (result.statusCode === 200) {
    // Rediriger vers result.link
    console.log('Lien de paiement:', result.link);
} else {
    console.error('Erreur:', result.message);
}
```

### 2. Paiement direct (`directPay`)

Envoie une demande de paiement directement sur le mobile de l'utilisateur.

```javascript
const directPaymentData = {
    amount: 1000,                    // Requis: montant en XAF
    phone: "677123456",              // Requis: numéro de téléphone (9 chiffres commençant par 6)
    medium: "mobile money",          // Optionnel: "mobile money" ou "orange money"
    name: "John Doe",                // Optionnel: nom du payeur
    email: "<EMAIL>",       // Optionnel: email
    userId: "user123",               // Optionnel: ID utilisateur
    externalId: "order456",          // Optionnel: ID externe
    message: "Paiement direct"       // Optionnel: message
};

const result = await fapshi.directPay(directPaymentData);
if (result.statusCode === 200) {
    console.log('Transaction ID:', result.transId);
    // Utiliser result.transId pour vérifier le statut
}
```

### 3. Vérification du statut (`paymentStatus`)

Vérifie le statut d'une transaction.

```javascript
const transId = "ABC123DEF";
const status = await fapshi.paymentStatus(transId);

if (status.statusCode === 200) {
    console.log('Statut:', status.status); // CREATED, SUCCESSFUL, FAILED, EXPIRED
    console.log('Montant:', status.amount);
    console.log('Email:', status.email);
}
```

### 4. Expiration de transaction (`expirePay`)

Expire une transaction en attente.

```javascript
const transId = "ABC123DEF";
const result = await fapshi.expirePay(transId);

if (result.statusCode === 200) {
    console.log('Transaction expirée:', result.transId);
}
```

### 5. Transactions par utilisateur (`userTrans`)

Récupère toutes les transactions d'un utilisateur.

```javascript
const userId = "user123";
const transactions = await fapshi.userTrans(userId);

if (transactions.statusCode === 200) {
    transactions.forEach(trans => {
        console.log(`Transaction ${trans.transId}: ${trans.status}`);
    });
}
```

### 6. Recherche de transactions (`searchTransactions`) - NOUVEAU

Recherche des transactions avec des filtres avancés.

```javascript
const filters = {
    status: "successful",            // Optionnel: created, successful, failed, expired
    medium: "mobile money",          // Optionnel: mobile money, orange money
    start: "2023-01-01",            // Optionnel: date de début (YYYY-MM-DD)
    end: "2023-12-31",              // Optionnel: date de fin (YYYY-MM-DD)
    amt: 1000,                      // Optionnel: montant exact
    limit: 20,                      // Optionnel: nombre de résultats (1-100, défaut: 10)
    sort: "desc"                    // Optionnel: asc ou desc (défaut: desc)
};

const results = await fapshi.searchTransactions(filters);
if (results.statusCode === 200) {
    console.log('Transactions trouvées:', results.length);
}
```

### 7. Solde du service (`getServiceBalance`) - NOUVEAU

Récupère le solde actuel du compte de service.

```javascript
const balance = await fapshi.getServiceBalance();
if (balance.statusCode === 200) {
    console.log(`Solde: ${balance.balance} ${balance.currency}`);
}
```

### 8. Validation de webhook (`validateWebhook`) - NOUVEAU

Valide qu'un webhook provient bien de Fapshi.

```javascript
// Dans votre endpoint webhook
app.post('/webhook/fapshi', async (req, res) => {
    const validation = await fapshi.validateWebhook(req.body);
    
    if (validation.valid) {
        // Traiter le webhook
        const transaction = validation.transaction;
        console.log('Webhook valide pour transaction:', transaction.transId);
        
        // Mettre à jour votre base de données
        await updatePaymentStatus(transaction);
        
        res.status(200).json({ message: 'Webhook processed' });
    } else {
        console.error('Webhook invalide:', validation.message);
        res.status(400).json({ message: 'Invalid webhook' });
    }
});
```

## Gestion des erreurs

Toutes les fonctions retournent un objet avec un `statusCode` :
- `200` : Succès
- `400` : Erreur de validation ou paramètres invalides
- `401` : Erreur d'authentification
- `404` : Ressource non trouvée
- `500` : Erreur serveur

```javascript
const result = await fapshi.initiatePay(data);
if (result.statusCode !== 200) {
    console.error(`Erreur ${result.statusCode}: ${result.message}`);
    // Gérer l'erreur appropriée
}
```

## Webhooks Fapshi

Fapshi envoie des webhooks pour les changements de statut :
- `SUCCESSFUL` : Paiement réussi
- `FAILED` : Paiement échoué
- `EXPIRED` : Lien de paiement expiré (après 24h)

Le corps du webhook contient les mêmes données que `paymentStatus`.

## Bonnes pratiques

1. **Toujours valider les webhooks** avec `validateWebhook()`
2. **Vérifier le statusCode** avant de traiter les réponses
3. **Utiliser des montants entiers** (pas de décimales)
4. **Stocker les transId** pour le suivi des paiements
5. **Implémenter une logique de retry** pour les vérifications de statut
6. **Utiliser des variables d'environnement** pour les credentials

## Exemple d'intégration complète

Voir les contrôleurs existants :
- `src/controllers/creditPurchaseController.js`
- `src/controllers/schoolCreditPaymentController.js`
- `src/controllers/paymentController.js`
