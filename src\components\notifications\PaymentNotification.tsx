"use client";

import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  CreditCard,
  X,
  Bell
} from 'lucide-react';

interface PaymentNotificationProps {
  isVisible: boolean;
  type: 'success' | 'failed' | 'pending' | 'expired';
  title: string;
  message: string;
  amount?: number;
  credits?: number;
  transactionId?: string;
  onClose: () => void;
  onAction?: () => void;
  actionLabel?: string;
  autoClose?: boolean;
  duration?: number;
}

export default function PaymentNotification({
  isVisible,
  type,
  title,
  message,
  amount,
  credits,
  transactionId,
  onClose,
  onAction,
  actionLabel,
  autoClose = true,
  duration = 5000
}: PaymentNotificationProps) {
  const [progress, setProgress] = useState(100);

  useEffect(() => {
    if (isVisible && autoClose && type !== 'pending') {
      const interval = setInterval(() => {
        setProgress(prev => {
          if (prev <= 0) {
            clearInterval(interval);
            onClose();
            return 0;
          }
          return prev - (100 / (duration / 100));
        });
      }, 100);

      return () => clearInterval(interval);
    }
  }, [isVisible, autoClose, type, duration, onClose]);

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-6 w-6 text-green-600" />;
      case 'failed':
        return <XCircle className="h-6 w-6 text-red-600" />;
      case 'expired':
        return <Clock className="h-6 w-6 text-orange-600" />;
      case 'pending':
        return <Clock className="h-6 w-6 text-blue-600 animate-pulse" />;
      default:
        return <Bell className="h-6 w-6 text-gray-600" />;
    }
  };

  const getColors = () => {
    switch (type) {
      case 'success':
        return {
          bg: 'bg-green-50 dark:bg-green-900/20',
          border: 'border-green-200 dark:border-green-800',
          text: 'text-green-800 dark:text-green-200',
          progress: 'bg-green-500'
        };
      case 'failed':
        return {
          bg: 'bg-red-50 dark:bg-red-900/20',
          border: 'border-red-200 dark:border-red-800',
          text: 'text-red-800 dark:text-red-200',
          progress: 'bg-red-500'
        };
      case 'expired':
        return {
          bg: 'bg-orange-50 dark:bg-orange-900/20',
          border: 'border-orange-200 dark:border-orange-800',
          text: 'text-orange-800 dark:text-orange-200',
          progress: 'bg-orange-500'
        };
      case 'pending':
        return {
          bg: 'bg-blue-50 dark:bg-blue-900/20',
          border: 'border-blue-200 dark:border-blue-800',
          text: 'text-blue-800 dark:text-blue-200',
          progress: 'bg-blue-500'
        };
      default:
        return {
          bg: 'bg-gray-50 dark:bg-gray-900/20',
          border: 'border-gray-200 dark:border-gray-800',
          text: 'text-gray-800 dark:text-gray-200',
          progress: 'bg-gray-500'
        };
    }
  };

  const colors = getColors();

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -100, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -100, scale: 0.95 }}
          className="fixed top-4 right-4 z-50 max-w-sm w-full"
        >
          <div className={`${colors.bg} ${colors.border} border rounded-lg shadow-lg overflow-hidden`}>
            {/* Progress bar */}
            {autoClose && type !== 'pending' && (
              <div className="h-1 bg-gray-200 dark:bg-gray-700">
                <motion.div
                  className={`h-full ${colors.progress}`}
                  initial={{ width: '100%' }}
                  animate={{ width: `${progress}%` }}
                  transition={{ duration: 0.1, ease: 'linear' }}
                />
              </div>
            )}

            <div className="p-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  {getIcon()}
                </div>
                
                <div className="ml-3 flex-1">
                  <h3 className={`text-sm font-medium ${colors.text}`}>
                    {title}
                  </h3>
                  <p className={`mt-1 text-sm ${colors.text} opacity-90`}>
                    {message}
                  </p>
                  
                  {/* Payment details */}
                  {(amount || credits || transactionId) && (
                    <div className="mt-2 space-y-1">
                      {credits && (
                        <div className="flex items-center text-xs opacity-75">
                          <CreditCard className="h-3 w-3 mr-1" />
                          <span>{credits} crédits</span>
                        </div>
                      )}
                      {amount && (
                        <div className="flex items-center text-xs opacity-75">
                          <span className="font-mono">{amount} XAF</span>
                        </div>
                      )}
                      {transactionId && (
                        <div className="flex items-center text-xs opacity-75">
                          <span className="font-mono truncate">{transactionId}</span>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Action button */}
                  {onAction && actionLabel && (
                    <div className="mt-3">
                      <button
                        onClick={onAction}
                        className={`text-xs font-medium ${colors.text} hover:underline`}
                      >
                        {actionLabel}
                      </button>
                    </div>
                  )}
                </div>

                <div className="ml-4 flex-shrink-0">
                  <button
                    onClick={onClose}
                    className={`${colors.text} hover:opacity-75 transition-opacity`}
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

// Hook pour gérer les notifications de paiement
export function usePaymentNotifications() {
  const [notifications, setNotifications] = useState<Array<{
    id: string;
    type: 'success' | 'failed' | 'pending' | 'expired';
    title: string;
    message: string;
    amount?: number;
    credits?: number;
    transactionId?: string;
    actionLabel?: string;
    onAction?: () => void;
    autoClose?: boolean;
  }>>([]);

  const addNotification = (notification: Omit<typeof notifications[0], 'id'>) => {
    const id = Date.now().toString();
    setNotifications(prev => [...prev, { ...notification, id }]);
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const showPaymentSuccess = (data: {
    amount: number;
    credits: number;
    transactionId: string;
  }) => {
    addNotification({
      type: 'success',
      title: '🎉 Paiement réussi !',
      message: 'Vos crédits ont été ajoutés à votre compte.',
      amount: data.amount,
      credits: data.credits,
      transactionId: data.transactionId,
      autoClose: true
    });
  };

  const showPaymentFailed = (data: {
    amount?: number;
    credits?: number;
    transactionId?: string;
    reason?: string;
  }) => {
    addNotification({
      type: 'failed',
      title: '❌ Paiement échoué',
      message: data.reason || 'Une erreur est survenue lors du paiement.',
      amount: data.amount,
      credits: data.credits,
      transactionId: data.transactionId,
      actionLabel: 'Réessayer',
      onAction: () => window.location.href = '/school-admin/buy-credit',
      autoClose: false
    });
  };

  const showPaymentPending = (data: {
    amount: number;
    credits: number;
    transactionId: string;
  }) => {
    addNotification({
      type: 'pending',
      title: '⏳ Paiement en cours',
      message: 'Votre paiement est en cours de traitement.',
      amount: data.amount,
      credits: data.credits,
      transactionId: data.transactionId,
      autoClose: false
    });
  };

  const showPaymentExpired = (data: {
    amount?: number;
    credits?: number;
    transactionId?: string;
  }) => {
    addNotification({
      type: 'expired',
      title: '⏰ Paiement expiré',
      message: 'Le lien de paiement a expiré après 24 heures.',
      amount: data.amount,
      credits: data.credits,
      transactionId: data.transactionId,
      actionLabel: 'Nouvel achat',
      onAction: () => window.location.href = '/school-admin/buy-credit',
      autoClose: false
    });
  };

  return {
    notifications,
    addNotification,
    removeNotification,
    showPaymentSuccess,
    showPaymentFailed,
    showPaymentPending,
    showPaymentExpired
  };
}
