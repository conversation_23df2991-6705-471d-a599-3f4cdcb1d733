"use client";

import { FileCheck2 } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import CircularLoader from "@/components/widgets/CircularLoader";
import React, { Suspense } from "react";
import useAuth from "@/app/hooks/useAuth";
import { SchoolAdminDashboardSkeleton } from "@/components/skeletons";
import StudentAttendance from "@/components/attendance/StudentAttendance";
import { useSearchParams } from "next/navigation";
import SubjectGradesPage from "@/components/grades/SubjectGradePage";

const BASE_URL = "/school-admin";

const navigation = {
  icon: FileCheck2,
  baseHref: `${BASE_URL}/grades`,
  title: "Grades",
};

export default function Page() {
  const { logout } = useAuth();
  const searchParams = useSearchParams();
  const { user } = useAuth();
  const classIdRaw = searchParams.get("classId");
  const subjectId = searchParams.get("subjectId");

  

  return (
    <Suspense>
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        {user && (
          <SubjectGradesPage
            user={user}
            classId_combine={classIdRaw as string}
            subjectId={subjectId as string}
          />
        )}
      </SchoolLayout>
    </Suspense>
  );
}
