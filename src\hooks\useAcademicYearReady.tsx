import { useEffect, useState } from 'react';
import { useAcademicYearContext } from '@/context/AcademicYearContext';

/**
 * Hook personnalisé pour attendre que l'année académique soit chargée
 * avant d'exécuter des opérations qui en dépendent
 */
export const useAcademicYearReady = () => {
  const { currentAcademicYear } = useAcademicYearContext();
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    if (currentAcademicYear) {
      setIsReady(true);
    } else {
      setIsReady(false);
    }
  }, [currentAcademicYear]);

  return {
    isReady,
    currentAcademicYear,
    isLoading: !currentAcademicYear
  };
};

/**
 * Hook pour exécuter une fonction callback quand l'année académique est prête
 */
export const useWhenAcademicYearReady = (
  callback: (academicYear: any) => void,
  dependencies: any[] = []
) => {
  const { currentAcademicYear } = useAcademicYearContext();

  useEffect(() => {
    if (currentAcademicYear) {
      callback(currentAcademicYear);
    }
  }, [currentAcademicYear, ...dependencies]);

  return {
    currentAcademicYear,
    isReady: !!currentAcademicYear
  };
};
