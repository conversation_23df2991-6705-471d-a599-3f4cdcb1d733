const fapshi = require('../src/utils/fapshi');

// Mock axios pour les tests
jest.mock('axios');
const axios = require('axios');

describe('Fapshi Integration Tests', () => {
  
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock des variables d'environnement
    process.env.FAPSHI_API_USER = 'test-user';
    process.env.FAPSHI_API_KEY = 'test-key';
  });

  describe('initiatePay', () => {
    test('should successfully initiate payment with valid data', async () => {
      const mockResponse = {
        status: 200,
        data: {
          link: 'https://sandbox.fapshi.com/pay/abc123',
          transId: 'ABC123DEF',
          message: 'Payment initiated successfully'
        }
      };
      
      axios.mockResolvedValue(mockResponse);

      const paymentData = {
        amount: 1000,
        email: '<EMAIL>',
        userId: 'user123'
      };

      const result = await fapshi.initiatePay(paymentData);
      
      expect(result.statusCode).toBe(200);
      expect(result.link).toBeDefined();
      expect(result.transId).toBeDefined();
    });

    test('should reject payment with invalid amount', async () => {
      const paymentData = {
        amount: 50, // Moins de 100 XAF
        email: '<EMAIL>'
      };

      const result = await fapshi.initiatePay(paymentData);
      
      expect(result.statusCode).toBe(400);
      expect(result.message).toContain('amount cannot be less than 100 XAF');
    });

    test('should reject payment with invalid email', async () => {
      const paymentData = {
        amount: 1000,
        email: 'invalid-email'
      };

      const result = await fapshi.initiatePay(paymentData);
      
      expect(result.statusCode).toBe(400);
      expect(result.message).toContain('invalid email format');
    });

    test('should reject payment with non-integer amount', async () => {
      const paymentData = {
        amount: 1000.50
      };

      const result = await fapshi.initiatePay(paymentData);
      
      expect(result.statusCode).toBe(400);
      expect(result.message).toContain('amount must be of type integer');
    });
  });

  describe('directPay', () => {
    test('should successfully initiate direct payment', async () => {
      const mockResponse = {
        status: 200,
        data: {
          transId: 'ABC123DEF',
          status: 'CREATED',
          message: 'Direct payment initiated'
        }
      };
      
      axios.mockResolvedValue(mockResponse);

      const paymentData = {
        amount: 1000,
        phone: '677123456'
      };

      const result = await fapshi.directPay(paymentData);
      
      expect(result.statusCode).toBe(200);
      expect(result.transId).toBeDefined();
    });

    test('should reject direct payment with invalid phone', async () => {
      const paymentData = {
        amount: 1000,
        phone: '123456789' // Ne commence pas par 6
      };

      const result = await fapshi.directPay(paymentData);
      
      expect(result.statusCode).toBe(400);
      expect(result.message).toContain('invalid phone number format');
    });

    test('should reject direct payment with invalid medium', async () => {
      const paymentData = {
        amount: 1000,
        phone: '677123456',
        medium: 'invalid medium'
      };

      const result = await fapshi.directPay(paymentData);
      
      expect(result.statusCode).toBe(400);
      expect(result.message).toContain('medium must be');
    });
  });

  describe('paymentStatus', () => {
    test('should successfully get payment status', async () => {
      const mockResponse = {
        status: 200,
        data: {
          transId: 'ABC123DEF',
          status: 'SUCCESSFUL',
          amount: 1000,
          email: '<EMAIL>'
        }
      };
      
      axios.mockResolvedValue(mockResponse);

      const result = await fapshi.paymentStatus('ABC123DEF');
      
      expect(result.statusCode).toBe(200);
      expect(result.status).toBe('SUCCESSFUL');
    });

    test('should reject invalid transaction ID', async () => {
      const result = await fapshi.paymentStatus('invalid-id');
      
      expect(result.statusCode).toBe(400);
      expect(result.message).toContain('invalid transaction id format');
    });
  });

  describe('searchTransactions', () => {
    test('should successfully search transactions with filters', async () => {
      const mockResponse = {
        status: 200,
        data: [
          {
            transId: 'ABC123',
            status: 'SUCCESSFUL',
            amount: 1000
          }
        ]
      };
      
      axios.mockResolvedValue(mockResponse);

      const filters = {
        status: 'successful',
        limit: 10
      };

      const result = await fapshi.searchTransactions(filters);
      
      expect(result.statusCode).toBe(200);
      expect(Array.isArray(result)).toBe(true);
    });

    test('should reject invalid status filter', async () => {
      const filters = {
        status: 'invalid-status'
      };

      const result = await fapshi.searchTransactions(filters);
      
      expect(result.statusCode).toBe(400);
      expect(result.message).toContain('invalid status filter');
    });

    test('should reject invalid date format', async () => {
      const filters = {
        start: '2023/01/01' // Format invalide
      };

      const result = await fapshi.searchTransactions(filters);
      
      expect(result.statusCode).toBe(400);
      expect(result.message).toContain('invalid start date format');
    });
  });

  describe('getServiceBalance', () => {
    test('should successfully get service balance', async () => {
      const mockResponse = {
        status: 200,
        data: {
          service: 'fapshi',
          balance: 50000,
          currency: 'XAF'
        }
      };
      
      axios.mockResolvedValue(mockResponse);

      const result = await fapshi.getServiceBalance();
      
      expect(result.statusCode).toBe(200);
      expect(result.balance).toBeDefined();
      expect(result.currency).toBe('XAF');
    });
  });

  describe('validateWebhook', () => {
    test('should successfully validate webhook', async () => {
      // Mock de paymentStatus pour la validation
      const mockStatusResponse = {
        statusCode: 200,
        transId: 'ABC123DEF',
        status: 'SUCCESSFUL',
        amount: 1000
      };

      // Mock de la fonction paymentStatus
      jest.spyOn(fapshi, 'paymentStatus').mockResolvedValue(mockStatusResponse);

      const webhookData = {
        transId: 'ABC123DEF',
        status: 'SUCCESSFUL',
        amount: 1000
      };

      const result = await fapshi.validateWebhook(webhookData);
      
      expect(result.valid).toBe(true);
      expect(result.transaction).toBeDefined();
    });

    test('should reject webhook with missing transId', async () => {
      const webhookData = {
        status: 'SUCCESSFUL',
        amount: 1000
      };

      const result = await fapshi.validateWebhook(webhookData);
      
      expect(result.statusCode).toBe(400);
      expect(result.message).toContain('webhook data must contain transId');
    });
  });

  describe('expirePay', () => {
    test('should successfully expire transaction', async () => {
      const mockResponse = {
        status: 200,
        data: {
          transId: 'ABC123DEF',
          status: 'EXPIRED',
          message: 'Transaction expired successfully'
        }
      };
      
      axios.mockResolvedValue(mockResponse);

      const result = await fapshi.expirePay('ABC123DEF');
      
      expect(result.statusCode).toBe(200);
      expect(result.transId).toBe('ABC123DEF');
    });
  });

  describe('userTrans', () => {
    test('should successfully get user transactions', async () => {
      const mockResponse = {
        status: 200,
        data: [
          {
            transId: 'ABC123',
            status: 'SUCCESSFUL',
            amount: 1000
          }
        ]
      };
      
      axios.mockResolvedValue(mockResponse);

      const result = await fapshi.userTrans('user123');
      
      expect(result.statusCode).toBe(200);
      expect(Array.isArray(result)).toBe(true);
    });

    test('should reject invalid user ID', async () => {
      const result = await fapshi.userTrans('');
      
      expect(result.statusCode).toBe(400);
      expect(result.message).toContain('invalid type, string expected');
    });
  });
});
