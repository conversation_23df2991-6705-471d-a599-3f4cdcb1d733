import { BASE_API_URL } from '@/app/services/AuthContext';
import { getTokenFromCookie } from '@/app/services/UserServices';

/**
 * Service de débogage pour tester les appels API liés aux crédits
 */
export class DebugCreditService {
  
  /**
   * Tester la récupération de la souscription d'école
   */
  static async testSchoolSubscription(schoolId: string) {
    console.log('🧪 Test de récupération de la souscription...');
    
    try {
      const token = getTokenFromCookie("idToken");
      const response = await fetch(`${BASE_API_URL}/school-subscription/${schoolId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });

      console.log('📡 Réponse HTTP:', response.status, response.statusText);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Erreur HTTP:', errorText);
        return null;
      }

      const data = await response.json();
      console.log('📦 Données reçues:', data);
      console.log('🎯 Souscription extraite:', data.subscription);
      
      return data;
    } catch (error) {
      console.error('❌ Erreur lors du test:', error);
      return null;
    }
  }

  /**
   * Tester la récupération des données d'école
   */
  static async testSchoolData(schoolId: string) {
    console.log('🧪 Test de récupération des données d\'école...');
    
    try {
      const token = getTokenFromCookie("idToken");
      const response = await fetch(`${BASE_API_URL}/school/get-school_id/${schoolId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });

      console.log('📡 Réponse HTTP:', response.status, response.statusText);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Erreur HTTP:', errorText);
        return null;
      }

      const data = await response.json();
      console.log('📦 Données école reçues:', data);
      console.log('💰 Crédits école:', data.credit);
      
      return data;
    } catch (error) {
      console.error('❌ Erreur lors du test:', error);
      return null;
    }
  }

  /**
   * Tester la récupération des achats de crédits
   */
  static async testCreditPurchases(schoolId: string) {
    console.log('🧪 Test de récupération des achats de crédits...');
    
    try {
      const token = getTokenFromCookie("idToken");
      const response = await fetch(`${BASE_API_URL}/credit-purchase/school/${schoolId}/history?limit=10`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });

      console.log('📡 Réponse HTTP:', response.status, response.statusText);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Erreur HTTP:', errorText);
        return null;
      }

      const data = await response.json();
      console.log('📦 Achats de crédits reçus:', data);
      console.log('🛒 Nombre d\'achats:', data.purchases?.length || 0);
      
      // Filtrer les achats en attente
      const pendingPurchases = data.purchases?.filter((p: any) => p.payment_status === 'pending') || [];
      console.log('⏳ Achats en attente:', pendingPurchases.length);
      
      return data;
    } catch (error) {
      console.error('❌ Erreur lors du test:', error);
      return null;
    }
  }

  /**
   * Test complet de tous les services
   */
  static async runFullTest(schoolId: string) {
    console.log('🚀 === TEST COMPLET DES SERVICES DE CRÉDITS ===');
    console.log('🏫 École ID:', schoolId);
    
    const results = {
      school: await this.testSchoolData(schoolId),
      subscription: await this.testSchoolSubscription(schoolId),
      purchases: await this.testCreditPurchases(schoolId)
    };

    console.log('📊 === RÉSUMÉ DES TESTS ===');
    console.log('École:', results.school ? '✅' : '❌');
    console.log('Souscription:', results.subscription ? '✅' : '❌');
    console.log('Achats:', results.purchases ? '✅' : '❌');

    if (results.school && results.subscription) {
      console.log('💰 Crédits école:', results.school.credit);
      console.log('💳 Balance souscription:', results.subscription.subscription?.credits_balance);
      console.log('🛒 Achats totaux:', results.subscription.subscription?.credits_purchased);
      console.log('📉 Crédits utilisés:', results.subscription.subscription?.credits_used);
    }

    return results;
  }
}

// Fonction utilitaire pour utiliser dans la console du navigateur
(window as any).debugCredits = DebugCreditService;
