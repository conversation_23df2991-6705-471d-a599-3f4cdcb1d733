const mongoose = require('mongoose');
const CreditSyncService = require('./src/utils/creditSyncService');
const School = require('./src/models/School');
const SchoolSubscription = require('./src/models/SchoolSubscription');
const CreditPurchase = require('./src/models/CreditPurchase');

// Configuration de la base de données
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/scholarify';

async function connectDB() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connexion à MongoDB réussie');
  } catch (error) {
    console.error('❌ Erreur de connexion à MongoDB:', error);
    process.exit(1);
  }
}

async function testCreditSync() {
  console.log('\n🧪 === TEST DE SYNCHRONISATION DES CRÉDITS ===\n');

  try {
    // 1. Trouver une école de test
    const school = await School.findOne();
    if (!school) {
      console.log('❌ Aucune école trouvée pour le test');
      return;
    }

    console.log(`🏫 École de test: ${school.name} (ID: ${school._id})`);

    // 2. Afficher l'état actuel
    console.log('\n📊 État actuel:');
    const currentStatus = await CreditSyncService.getCreditStatus(school._id);
    console.log('Current Status:', JSON.stringify(currentStatus, null, 2));

    // 3. Trouver les achats de crédits de cette école
    const purchases = await CreditPurchase.find({ school_id: school._id });
    console.log(`\n💳 Achats de crédits trouvés: ${purchases.length}`);
    
    purchases.forEach((purchase, index) => {
      console.log(`  ${index + 1}. ${purchase.purchase_id} - ${purchase.credits_purchased} crédits - Statut: ${purchase.payment_status}`);
    });

    // 4. Synchroniser les crédits
    console.log('\n🔄 Synchronisation des crédits...');
    const syncResult = await CreditSyncService.syncSchoolCredits(school._id);
    
    if (syncResult.success) {
      console.log('✅ Synchronisation réussie!');
      console.log(`   - Crédits école: ${syncResult.schoolCredits}`);
      console.log(`   - Balance souscription: ${syncResult.subscriptionBalance}`);
      console.log(`   - Total achats: ${syncResult.totalPurchases}`);
    } else {
      console.log('❌ Erreur de synchronisation:', syncResult.error);
    }

    // 5. Vérifier l'état après synchronisation
    console.log('\n📊 État après synchronisation:');
    const newStatus = await CreditSyncService.getCreditStatus(school._id);
    console.log('New Status:', JSON.stringify(newStatus, null, 2));

    // 6. Test de déduction de crédits
    if (newStatus && newStatus.subscriptionBalance > 0) {
      console.log('\n🔻 Test de déduction de crédits...');
      const deductResult = await CreditSyncService.deductCredits(
        school._id, 
        1, 
        'Test de déduction', 
        'test-user-id'
      );
      
      if (deductResult.success) {
        console.log('✅ Déduction réussie!');
        console.log(`   - Balance avant: ${deductResult.balanceBefore}`);
        console.log(`   - Balance après: ${deductResult.balanceAfter}`);
        console.log(`   - Crédits déduits: ${deductResult.creditsDeducted}`);
      } else {
        console.log('❌ Erreur de déduction:', deductResult.error);
      }
    }

  } catch (error) {
    console.error('❌ Erreur lors du test:', error);
  }
}

async function testPendingPurchases() {
  console.log('\n🧪 === TEST DES ACHATS EN ATTENTE ===\n');

  try {
    // Trouver les achats en attente
    const pendingPurchases = await CreditPurchase.find({ payment_status: 'pending' })
      .populate('school_id', 'name')
      .populate('purchased_by', 'name email')
      .limit(5);

    console.log(`📋 Achats en attente trouvés: ${pendingPurchases.length}`);

    pendingPurchases.forEach((purchase, index) => {
      console.log(`\n${index + 1}. Achat: ${purchase.purchase_id}`);
      console.log(`   - École: ${purchase.school_id?.name || 'N/A'}`);
      console.log(`   - Crédits: ${purchase.credits_purchased}`);
      console.log(`   - Montant: ${purchase.total_amount} ${purchase.currency}`);
      console.log(`   - Date: ${purchase.purchase_date}`);
      console.log(`   - Acheteur: ${purchase.purchased_by?.name || 'N/A'}`);
      
      if (purchase.payment_gateway_response?.link) {
        console.log(`   - Lien de paiement: ${purchase.payment_gateway_response.link}`);
      }
    });

  } catch (error) {
    console.error('❌ Erreur lors du test des achats en attente:', error);
  }
}

async function main() {
  await connectDB();
  
  await testCreditSync();
  await testPendingPurchases();
  
  console.log('\n✅ Tests terminés');
  process.exit(0);
}

// Exécuter le script
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Erreur fatale:', error);
    process.exit(1);
  });
}

module.exports = {
  testCreditSync,
  testPendingPurchases
};
