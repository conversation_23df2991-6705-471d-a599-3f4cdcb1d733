"use client";

import { DollarSign } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import CircularLoader from "@/components/widgets/CircularLoader";
import React, { Suspense, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import useAuth from "@/app/hooks/useAuth";
import DataTableFix from "@/components/utils/TableFix";
import { motion } from "framer-motion";
import NotificationCard from "@/components/NotificationCard";
import { createSuccessNotification, createErrorNotification, NotificationState } from "@/app/types/notification";
import { FeeSchema } from "@/app/models/FeesModel";
import { getFees, getFeesBySchoolId, createFee, updateFee, deleteFee, deleteMultipleFees } from "@/app/services/FeesServices";
import FeeTypeModal from "@/components/modals/FeeTypeModal";
import PasswordConfirmDeleteModal from "@/components/modals/PasswordConfirmDeleteModal";
import FeeTypeSkeleton from "@/components/skeletons/FeeTypeSkeleton";
import { verifyPassword } from "@/app/services/UserServices";

const BASE_URL = "/school-admin";

const navigation = {
  icon: DollarSign,
  baseHref: `${BASE_URL}/fees`,
  title: "Fee Types"
};

function FeesContent() {
  const [fees, setFees] = useState<FeeSchema[]>([]);
  const [loadingData, setLoadingData] = useState(true);
  const [selectedFees, setSelectedFees] = useState<FeeSchema[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [feeToEdit, setFeeToEdit] = useState<FeeSchema | null>(null);
  const [feeToDelete, setFeeToDelete] = useState<FeeSchema | null>(null);
  const [deleteType, setDeleteType] = useState<"single" | "multiple">("single");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<NotificationState | null>(null);
  const [clearSelection, setClearSelection] = useState(false);
  const { user } = useAuth();
  
  // Columns for the table
  const columns = [
    {
      header: "Fee Type",
      accessor: (row: FeeSchema) => (
        <span className="font-medium">{row.fee_type}</span>
      )
    },
    {
      header: "Amount",
      accessor: (row: FeeSchema) => (
        <span className="font-mono text-green-600 dark:text-green-400">
          {row.amount.toFixed(2)}
        </span>
      )
    },
  ];

  // Actions for the table
  const actions = [
    {
      label: "Edit",
      onClick: (fee: FeeSchema) => {
        handleEditFee(fee);
      },
    },
    {
      label: "Delete",
      onClick: (fee: FeeSchema) => {
        handleDeleteFee(fee);
      },
    },
  ];

  // Load fees on page load
  useEffect(() => {
    fetchFees();
  }, []);

  const fetchFees = async () => {
    try {
      setLoadingData(true);
      if (user && user.school_ids && user.school_ids.length > 0) {
        const schoolId = user.school_ids[0];
        const feesData = await getFeesBySchoolId(schoolId);
        setFees(feesData);
      } else {
        const feesData = await getFees();
        setFees(feesData);
      }
    } catch (error) {
      console.error("Error fetching fees:", error);
      setSubmitStatus(createErrorNotification("Failed to fetch fees"));
    } finally {
      setLoadingData(false);
    }
  };

  // Handle creating new fee
  const handleCreateFee = () => {
    setFeeToEdit(null);
    setIsModalOpen(true);
  };

  // Handle editing fee
  const handleEditFee = (fee: FeeSchema) => {
    setFeeToEdit(fee);
    setIsModalOpen(true);
  };

  // Handle deleting single fee
  const handleDeleteFee = (fee: FeeSchema) => {
    setFeeToDelete(fee);
    setDeleteType("single");
    setIsDeleteModalOpen(true);
  };

  // Handle deleting multiple fees
  const handleDeleteMultiple = (selectedIds: string[]) => {
    setDeleteType("multiple");
    setFeeToDelete(null);
    setIsDeleteModalOpen(true);
  };

  // Handle selection change
  const handleSelectionChange = (selected: FeeSchema[]) => {
    setSelectedFees(selected);
  };

  // Handle save (create or update)
  const handleSave = async (feeData: any) => {
    setIsSubmitting(true);
    try {
      if (feeToEdit) {
        // Update existing fee
        await updateFee(feeToEdit._id, feeData);
        setSubmitStatus(createSuccessNotification("Fee type updated successfully"));
      } else {
        // Create new fee
        await createFee(feeData);
        setSubmitStatus(createSuccessNotification("Fee type created successfully"));
      }

      setIsModalOpen(false);
      setFeeToEdit(null);
      await fetchFees(); // Refresh the list
    } catch (error) {
      console.error("Error saving fee:", error);
      setSubmitStatus(createErrorNotification("Failed to save fee type"));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete confirmation with password
  const handleDeleteConfirm = async (password: string) => {
    setIsSubmitting(true);
    try {
      // Verify password first
      if (!user?.email) {
        throw new Error("User email not found");
      }

      await verifyPassword(user.email, password);

      if (deleteType === "single" && feeToDelete) {
        // Delete single fee
        await deleteFee(feeToDelete._id);
        setSubmitStatus(createSuccessNotification("Fee type deleted successfully"));
      } else if (deleteType === "multiple" && selectedFees.length > 0) {
        // Delete multiple fees
        const ids = selectedFees.map(fee => fee._id);
        await deleteMultipleFees(ids);
        setSubmitStatus(createSuccessNotification(`${selectedFees.length} fee types deleted successfully`));
        setSelectedFees([]);
        setClearSelection(true);
      }

      setIsDeleteModalOpen(false);
      setFeeToDelete(null);
      await fetchFees(); // Refresh the list
    } catch (error) {
      console.error("Error deleting fee type(s):", error);
      if (error instanceof Error && error.message.includes("password")) {
        setSubmitStatus(createErrorNotification("Invalid password"));
      } else {
        setSubmitStatus(createErrorNotification("Failed to delete fee type(s)"));
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loadingData) {
    return <FeeTypeSkeleton />;
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4 text-text">Fee Types Management</h1>

      {submitStatus && (
        <div className="mb-4">
          <NotificationCard
            type={submitStatus.type}
            title={submitStatus.title}
            message={submitStatus.message}
            onClose={() => setSubmitStatus(null)}
            isVisible={true}
          />
        </div>
      )}

      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        transition={{ type: 'spring', stiffness: 300 }}
        onClick={handleCreateFee}
        className="mb-4 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors"
      >
        Add New Fee Type
      </motion.button>

      <DataTableFix<FeeSchema>
        data={fees}
        columns={columns}
        actions={actions}
        defaultItemsPerPage={10}
        onSelectionChange={handleSelectionChange}
        handleDeleteMultiple={handleDeleteMultiple}
        clearSelection={clearSelection}
        onSelectionCleared={() => setClearSelection(false)}
      />

      {/* Fee Type Modal */}
      <FeeTypeModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setFeeToEdit(null);
        }}
        onSave={handleSave}
        fee={feeToEdit}
        isSubmitting={isSubmitting}
      />

      {/* Delete Confirmation Modal with Password */}
      <PasswordConfirmDeleteModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setFeeToDelete(null);
        }}
        onConfirm={handleDeleteConfirm}
        title={
          deleteType === "single"
            ? "Delete Fee Type"
            : "Delete Selected Fee Types"
        }
        message={
          deleteType === "single"
            ? "Are you sure you want to delete this fee type? This action cannot be undone."
            : `Are you sure you want to delete ${selectedFees.length} selected fee types? This action cannot be undone.`
        }
        itemName={
          deleteType === "single" && feeToDelete
            ? feeToDelete.fee_type
            : undefined
        }
        itemCount={deleteType === "multiple" ? selectedFees.length : undefined}
        type={deleteType}
      />
    </div>
  );
}

export default function Page() {
  const { logout } = useAuth();
  return (
    <Suspense fallback={
      <div>
        <div className="flex justify-center items-center h-screen absolute top-0 left-0 z-50">
          <CircularLoader size={32} color="teal" />
        </div>
      </div>
    }>
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        <FeesContent />
      </SchoolLayout>
    </Suspense>
  );
}
