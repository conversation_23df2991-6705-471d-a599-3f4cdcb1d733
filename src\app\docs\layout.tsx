"use client";

import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { 
    BookOpen, 
    Users, 
    Code, 
    Home, 
    Search,
    Menu,
    X,
    ChevronLeft,
    ChevronRight,
    ArrowLeft
} from "lucide-react";
import Logo from "@/components/widgets/Logo";
import ThemeToggle from "@/components/ThemeToggle";

interface DocsLayoutProps {
    children: React.ReactNode;
}

export default function DocsLayout({ children }: DocsLayoutProps) {
    const router = useRouter();
    const [sidebarOpen, setSidebarOpen] = useState(false);
    const [mounted, setMounted] = useState(false);
    const [currentPath, setCurrentPath] = useState("");

    useEffect(() => {
        setMounted(true);
        if (typeof window !== 'undefined') {
            setCurrentPath(window.location.pathname);
        }
    }, []);

    const navigationItems = [
        {
            title: "Getting Started",
            icon: <BookOpen className="w-5 h-5" />,
            href: "/docs/getting-started",
            description: "Setup and configuration guide"
        },
        {
            title: "User Management",
            icon: <Users className="w-5 h-5" />,
            href: "/docs/user-management",
            description: "Managing users, roles and permissions"
        },
        {
            title: "API Reference",
            icon: <Code className="w-5 h-5" />,
            href: "/docs/api-reference",
            description: "Complete API documentation"
        }
    ];

    const handleBackToHome = () => {
        router.push("/");
    };

    const getCurrentPageTitle = () => {
        if (currentPath.includes('/getting-started')) return 'Getting Started';
        if (currentPath.includes('/user-management')) return 'User Management';
        if (currentPath.includes('/api-reference')) return 'API Reference';
        return 'Documentation';
    };

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
            {/* Header */}
            <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-40">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        {/* Logo and Navigation */}
                        <div className="flex items-center space-x-4">
                            <button
                                onClick={handleBackToHome}
                                className="flex items-center space-x-2 hover:opacity-80 transition-opacity"
                            >
                                <Logo />
                            </button>
                            <div className="hidden md:block">
                                <span className="text-gray-400 dark:text-gray-500">/</span>
                                <span className="ml-2 text-lg font-semibold text-gray-900 dark:text-white">
                                    {getCurrentPageTitle()}
                                </span>
                            </div>
                        </div>

                        <div className="flex items-center space-x-4">
                            {/* Back to Home Button */}
                            <button
                                onClick={handleBackToHome}
                                className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:text-teal-600 dark:hover:text-teal-300 transition-colors"
                            >
                                <Home className="w-4 h-4" />
                                <span className="hidden sm:inline">Back to Home</span>
                            </button>

                            <ThemeToggle />

                            {/* Mobile menu button */}
                            <button
                                onClick={() => setSidebarOpen(!sidebarOpen)}
                                className="md:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700"
                            >
                                {sidebarOpen ? (
                                    <X className="w-6 h-6" />
                                ) : (
                                    <Menu className="w-6 h-6" />
                                )}
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div className="flex gap-8">
                    {/* Sidebar */}
                    <aside className={`${
                        sidebarOpen ? 'block' : 'hidden'
                    } md:block w-full md:w-64 flex-shrink-0`}>
                        <div className="sticky top-24">
                            <nav className="space-y-2">
                                {navigationItems.map((item) => {
                                    const isActive = currentPath === item.href;
                                    return (
                                        <button
                                            key={item.href}
                                            onClick={() => {
                                                router.push(item.href);
                                                setSidebarOpen(false);
                                            }}
                                            className={`w-full flex items-start space-x-3 p-3 rounded-lg transition-colors group border ${
                                                isActive
                                                    ? 'bg-teal-50 dark:bg-teal-900/20 border-teal-200 dark:border-teal-800 shadow-sm'
                                                    : 'border-transparent hover:bg-white dark:hover:bg-gray-800 hover:border-gray-200 dark:hover:border-gray-700 hover:shadow-sm'
                                            }`}
                                        >
                                        <div className={`flex-shrink-0 ${
                                            isActive
                                                ? 'text-teal-600 dark:text-teal-300'
                                                : 'text-gray-500 dark:text-gray-400 group-hover:text-teal-600 dark:group-hover:text-teal-300'
                                        }`}>
                                            {item.icon}
                                        </div>
                                        <div className="flex-1 text-left">
                                            <div className={`text-sm font-medium ${
                                                isActive
                                                    ? 'text-teal-600 dark:text-teal-300'
                                                    : 'text-gray-900 dark:text-white group-hover:text-teal-600 dark:group-hover:text-teal-300'
                                            }`}>
                                                {item.title}
                                            </div>
                                            <div className={`text-xs mt-1 ${
                                                isActive
                                                    ? 'text-teal-500 dark:text-teal-400'
                                                    : 'text-gray-500 dark:text-gray-400'
                                            }`}>
                                                {item.description}
                                            </div>
                                        </div>
                                    </button>
                                    );
                                })}
                            </nav>

                            {/* Quick Links */}
                            <div className="mt-8 p-4 bg-teal-50 dark:bg-teal-900/20 rounded-lg border border-teal-200 dark:border-teal-800">
                                <h3 className="text-sm font-semibold text-teal-900 dark:text-teal-100 mb-2">
                                    Quick Links
                                </h3>
                                <div className="space-y-2 text-sm">
                                    <a 
                                        href="#" 
                                        className="block text-teal-700 dark:text-teal-300 hover:text-teal-800 dark:hover:text-teal-200"
                                    >
                                        System Requirements
                                    </a>
                                    <a 
                                        href="#" 
                                        className="block text-teal-700 dark:text-teal-300 hover:text-teal-800 dark:hover:text-teal-200"
                                    >
                                        Troubleshooting
                                    </a>
                                    <a 
                                        href="#" 
                                        className="block text-teal-700 dark:text-teal-300 hover:text-teal-800 dark:hover:text-teal-200"
                                    >
                                        Release Notes
                                    </a>
                                </div>
                            </div>
                        </div>
                    </aside>

                    {/* Main Content */}
                    <main className="flex-1 min-w-0">
                        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                            {children}
                        </div>
                    </main>
                </div>
            </div>

            {/* Mobile sidebar overlay */}
            {sidebarOpen && (
                <div 
                    className="fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden"
                    onClick={() => setSidebarOpen(false)}
                />
            )}
        </div>
    );
}