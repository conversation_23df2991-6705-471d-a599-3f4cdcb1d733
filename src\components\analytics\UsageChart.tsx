"use client";

import React, { useState, useEffect } from 'react';
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend
} from 'recharts';
import { Calendar, TrendingUp, TrendingDown } from 'lucide-react';
import { motion } from 'framer-motion';
import { getCreditUsageAnalytics, UsageAnalyticsResponse } from '@/app/services/SubscriptionServices';

interface UsageData {
  date: string;
  credits_purchased: number;
  credits_used: number;
  credits_balance: number;
  students_registered: number;
}

interface UsageChartProps {
  schoolId: string;
  period?: 'week' | 'month' | 'quarter' | 'year';
  className?: string;
}

const UsageChart: React.FC<UsageChartProps> = ({ 
  schoolId, 
  period = 'month',
  className = '' 
}) => {
  const [data, setData] = useState<UsageData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState(period);

  // Données de démonstration - à remplacer par de vraies données
  const generateDemoData = (period: string): UsageData[] => {
    const now = new Date();
    const data: UsageData[] = [];
    
    let days = 30;
    let format = 'DD/MM';
    
    switch (period) {
      case 'week':
        days = 7;
        format = 'ddd';
        break;
      case 'month':
        days = 30;
        format = 'DD/MM';
        break;
      case 'quarter':
        days = 90;
        format = 'MMM';
        break;
      case 'year':
        days = 365;
        format = 'MMM YYYY';
        break;
    }

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      
      const baseCredits = 100 + Math.random() * 50;
      const used = Math.floor(Math.random() * 20);
      const purchased = i % 7 === 0 ? Math.floor(Math.random() * 50) : 0;
      
      data.push({
        date: date.toLocaleDateString('fr-FR', { 
          day: period === 'week' ? undefined : '2-digit',
          month: period === 'year' ? 'short' : '2-digit',
          year: period === 'year' ? 'numeric' : undefined,
          weekday: period === 'week' ? 'short' : undefined
        }),
        credits_purchased: purchased,
        credits_used: used,
        credits_balance: Math.max(0, baseCredits - used + purchased),
        students_registered: used
      });
    }
    
    return data;
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        if (!schoolId) {
          throw new Error('School ID is required');
        }

        // Utiliser les vraies données de l'API
        const response = await getCreditUsageAnalytics(schoolId, selectedPeriod);
        setData(response.data);
        console.log("Données reçues:", response.data);

      } catch (err: any) {
        console.error('Error fetching usage analytics:', err);
        // Fallback vers les données de démonstration en cas d'erreur
        setData(generateDemoData(selectedPeriod));
        setError(err.message || 'Erreur lors du chargement des données');
      } finally {
        setLoading(false);
      }
    };

    if (schoolId) {
      fetchData();
    }
  }, [schoolId, selectedPeriod]);

  const periods = [
    { value: 'week', label: 'Semaine' },
    { value: 'month', label: 'Mois' },
    { value: 'quarter', label: 'Trimestre' },
    { value: 'year', label: 'Année' }
  ];

  const calculateTrend = () => {
    if (data.length < 2) return { value: 0, isPositive: true };

    const recent = data.slice(-7);
    const previous = data.slice(-14, -7);
    if(previous.length === 0) return { value: 0, isPositive: true };
    const recentAvg = recent.reduce((sum, d) => sum + d.credits_used, 0) / recent.length;
    const previousAvg = previous.reduce((sum, d) => sum + d.credits_used, 0) / previous.length;

    // Éviter la division par zéro qui cause NaN
    if (previousAvg === 0) {
      // Si pas d'utilisation précédente mais utilisation récente, c'est une augmentation
      if (recentAvg > 0) {
        return { value: 100, isPositive: true };
      }
      // Si aucune utilisation dans les deux périodes
      return { value: 0, isPositive: true };
    }

    const change = ((recentAvg - previousAvg) / previousAvg) * 100;
    return {
      value: Math.abs(change),
      isPositive: change >= 0
    };
  };

  const trend = calculateTrend();
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 dark:text-white mb-2">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {entry.value}
              {entry.dataKey === 'credits_balance' && ' crédits'}
              {entry.dataKey === 'credits_used' && ' crédits'}
              {entry.dataKey === 'credits_purchased' && ' crédits'}
              {entry.dataKey === 'students_registered' && ' étudiants'}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
          <div className="h-80 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 ${className}`}>
        <div className="text-center py-8">
          <p className="text-red-600 dark:text-red-400 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm"
          >
            Réessayer
          </button>
        </div>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 ${className}`}>
        <div className="text-center py-8">
          <p className="text-gray-600 dark:text-gray-400">Aucune donnée disponible</p>
        </div>
      </div>
    );
  }

  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 ${className}`}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
            <TrendingUp className="h-5 w-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Utilisation des Crédits
            </h3>
            <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
              <span>Tendance:</span>
              {trend.isPositive ? (
                <TrendingUp className="h-4 w-4 text-green-500" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-500" />
              )}
              <span className={trend.isPositive ? 'text-green-600' : 'text-red-600'}>
                {parseInt(trend.value.toFixed(1)) || 0}%
              </span>
            </div>
          </div>
        </div>

        {/* Period Selector */}
        <div className="flex items-center space-x-2">
          <Calendar className="h-4 w-4 text-gray-400" />
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value as any)}
            className="text-sm border border-gray-300 dark:border-gray-600 rounded-md px-3 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
          >
            {periods.map((p) => (
              <option key={p.value} value={p.value}>
                {p.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Chart */}
      <div className="h-80 w-full">
        <ResponsiveContainer width="100%" height={320}>
          <AreaChart
            data={data}
            margin={{
              top: 10,
              right: 30,
              left: 0,
              bottom: 0,
            }}
          >
            <defs>
              <linearGradient id="colorBalance" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.3} />
                <stop offset="95%" stopColor="#3B82F6" stopOpacity={0} />
              </linearGradient>
              <linearGradient id="colorUsed" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#EF4444" stopOpacity={0.3} />
                <stop offset="95%" stopColor="#EF4444" stopOpacity={0} />
              </linearGradient>
              <linearGradient id="colorPurchased" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#10B981" stopOpacity={0.3} />
                <stop offset="95%" stopColor="#10B981" stopOpacity={0} />
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
            <XAxis 
              dataKey="date" 
              axisLine={false}
              tick={{ fontSize: 12, fill: '#6B7280' }}
            />
            <YAxis 
              axisLine={false}
              tick={{ fontSize: 12, fill: '#6B7280' }}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            
            <Area
              type="monotone"
              dataKey="credits_balance"
              stroke="#3B82F6"
              strokeWidth={2}
              fillOpacity={1}
              fill="url(#colorBalance)"
              name="Solde"
            />
            <Area
              type="monotone"
              dataKey="credits_used"
              stroke="#EF4444"
              strokeWidth={2}
              fillOpacity={1}
              fill="url(#colorUsed)"
              name="Utilisés"
            />
            <Area
              type="monotone"
              dataKey="credits_purchased"
              stroke="#10B981"
              strokeWidth={2}
              fillOpacity={1}
              fill="url(#colorPurchased)"
              name="Achetés"
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>

      {/* Legend */}
      <div className="mt-4 flex flex-wrap gap-4 text-sm">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-blue-500 rounded"></div>
          <span className="text-gray-600 dark:text-gray-400">Solde des crédits</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-red-500 rounded"></div>
          <span className="text-gray-600 dark:text-gray-400">Crédits utilisés</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-green-500 rounded"></div>
          <span className="text-gray-600 dark:text-gray-400">Crédits achetés</span>
        </div>
      </div>
    </motion.div>
  );
};

export default UsageChart;
