const mongoose = require('mongoose');

const CreditPurchaseSchema = new mongoose.Schema({
  // Référence à l'école
  school_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'School',
    required: true
  },
  
  // Référence à la souscription de l'école
  subscription_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SchoolSubscription',
    required: true
  },
  
  // Informations de transaction
  transaction_id: {
    type: String,
    required: true,
    unique: true
  },
  
  purchase_id: {
    type: String,
    required: true,
    unique: true
  },
  
  // Détails de l'achat
  credits_purchased: {
    type: Number,
    required: true,
    min: 1
  },
  
  price_per_credit: {
    type: Number,
    required: true,
    default: 3000
  },
  
  total_amount: {
    type: Number,
    required: true
  },
  
  currency: {
    type: String,
    default: 'XAF', // Franc CFA
    required: true
  },
  
  // Informations de paiement
  payment_method: {
    type: String,
    enum: ['fapshi', 'manual', 'gift', 'promotion'],
    required: true
  },
  
  payment_status: {
    type: String,
    enum: [
      'initiated',        // Transaction créée mais pas encore envoyée à Fapshi
      'pending',          // Transaction envoyée à Fapshi, en attente de paiement
      'processing',       // Paiement en cours de traitement
      'completed',        // Paiement complété avec succès
      'failed',           // Paiement échoué
      'cancelled',        // Paiement annulé par l'utilisateur
      'expired',          // Transaction expirée
      'refunded',         // Transaction remboursée
      'refund_completed', // Remboursement effectué avec succès
      'abandoned'         // Transaction abandonnée (pas de réponse Fapshi)
    ],
    required: true,
    default: 'initiated'
  },
  
  payment_gateway_response: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  
  // Dates importantes
  purchase_date: {
    type: Date,
    default: Date.now
  },
  
  payment_completed_date: {
    type: Date
  },
  
  // Informations utilisateur
  purchased_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  purchaser_email: {
    type: String,
    required: true
  },
  
  // Informations de facturation
  billing_info: {
    name: String,
    email: String,
    phone: String,
    address: String,
    organization: String
  },
  
  // Promotion ou réduction
  promotion_code: {
    type: String
  },
  
  discount_amount: {
    type: Number,
    default: 0
  },
  
  discount_percentage: {
    type: Number,
    default: 0
  },
  
  // Métadonnées
  notes: {
    type: String,
    maxlength: 1000
  },
  
  admin_notes: {
    type: String,
    maxlength: 1000
  },
  
  // Informations de remboursement
  refund_info: {
    refund_date: Date,
    refund_amount: Number,
    refund_reason: String,
    refunded_by: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  },
  
  // Statut de traitement
  is_processed: {
    type: Boolean,
    default: false
  },
  
  processed_date: {
    type: Date
  },
  
  processed_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Index pour optimiser les requêtes
CreditPurchaseSchema.index({ school_id: 1, purchase_date: -1 });
CreditPurchaseSchema.index({ transaction_id: 1 });
CreditPurchaseSchema.index({ purchase_id: 1 });
CreditPurchaseSchema.index({ payment_status: 1 });
CreditPurchaseSchema.index({ purchased_by: 1 });

// Méthodes d'instance
CreditPurchaseSchema.methods.markAsCompleted = function(gateway_response = {}) {
  this.payment_status = 'completed';
  this.payment_completed_date = new Date();
  this.payment_gateway_response = gateway_response;
  this.is_processed = true;
  this.processed_date = new Date();
  
  return this.save();
};

CreditPurchaseSchema.methods.markAsFailed = function(reason = '') {
  this.payment_status = 'failed';
  this.notes = reason;
  
  return this.save();
};

CreditPurchaseSchema.methods.processRefund = function(refund_amount, reason, refunded_by) {
  this.payment_status = 'refunded';
  this.refund_info = {
    refund_date: new Date(),
    refund_amount: refund_amount,
    refund_reason: reason,
    refunded_by: refunded_by
  };
  
  return this.save();
};

// Méthodes statiques
CreditPurchaseSchema.statics.generatePurchaseId = function() {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `CP_${timestamp}_${random}`;
};

CreditPurchaseSchema.statics.getBySchoolId = function(school_id, limit = 50) {
  return this.find({ school_id })
    .populate('purchased_by', 'name email')
    .sort({ purchase_date: -1 })
    .limit(limit);
};

CreditPurchaseSchema.statics.getTotalPurchasedCredits = function(school_id) {
  return this.aggregate([
    { $match: { school_id: new mongoose.Types.ObjectId(school_id), payment_status: 'completed' } },
    { $group: { _id: null, total_credits: { $sum: '$credits_purchased' }, total_amount: { $sum: '$total_amount' } } }
  ]);
};

CreditPurchaseSchema.statics.getMonthlyStats = function(school_id, year, month) {
  const startDate = new Date(year, month - 1, 1);
  const endDate = new Date(year, month, 0);
  
  return this.aggregate([
    {
      $match: {
        school_id: new mongoose.Types.ObjectId(school_id),
        payment_status: 'completed',
        payment_completed_date: { $gte: startDate, $lte: endDate }
      }
    },
    {
      $group: {
        _id: null,
        total_purchases: { $sum: 1 },
        total_credits: { $sum: '$credits_purchased' },
        total_amount: { $sum: '$total_amount' }
      }
    }
  ]);
};

// Middleware pre-save
CreditPurchaseSchema.pre('save', function(next) {
  // Générer un purchase_id si pas encore défini
  if (!this.purchase_id) {
    this.purchase_id = this.constructor.generatePurchaseId();
  }
  
  // Calculer le montant total si pas encore défini
  if (!this.total_amount) {
    this.total_amount = (this.credits_purchased * this.price_per_credit) - this.discount_amount;
  }
  
  // Marquer comme traité si le paiement est complété
  if (this.payment_status === 'completed' && !this.is_processed) {
    this.is_processed = true;
    this.processed_date = new Date();
  }
  
  next();
});

// Middleware post-save pour synchroniser les crédits
CreditPurchaseSchema.post('save', async function(doc) {
  if (doc.payment_status === 'completed' && doc.is_processed) {
    try {
      const CreditSyncService = require('../utils/creditSyncService');

      console.log(`🔄 Synchronisation automatique des crédits après paiement: ${doc.purchase_id}`);

      // Synchroniser les crédits de l'école
      const syncResult = await CreditSyncService.syncSchoolCredits(doc.school_id, doc._id);

      if (syncResult.success) {
        console.log(`✅ Synchronisation réussie - Crédits école: ${syncResult.schoolCredits}, Balance souscription: ${syncResult.subscriptionBalance}`);
      } else {
        console.error(`❌ Erreur de synchronisation: ${syncResult.error}`);
      }
    } catch (error) {
      console.error('Error synchronizing credits after purchase:', error);
    }
  }
});

const CreditPurchase = mongoose.models.CreditPurchase || mongoose.model('CreditPurchase', CreditPurchaseSchema);

module.exports = CreditPurchase;
