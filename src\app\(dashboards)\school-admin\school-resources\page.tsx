"use client";

import { SchoolResourceType } from "@/app/models/SchoolResources";
import { BookText } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import CircularLoader from "@/components/widgets/CircularLoader";
import React, { Suspense, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import useAuth from "@/app/hooks/useAuth";
import DataTableFix from "@/components/utils/TableFix";
import { motion } from "framer-motion";
import NotificationCard from "@/components/NotificationCard";
import {
  createSuccessNotification,
  createErrorNotification,
  NotificationState,
} from "@/app/types/notification";
import { SchoolResourceSchema } from "@/app/models/SchoolResources";
import {
  getSchoolResources,
  getSchoolResourcesBySchoolId,
  createSchoolResource,
  updateSchoolResource,
  deleteSchoolResource,
  deleteMultipleSchoolResources,
} from "@/app/services/SchoolResourcesServices";
import SchoolResourceModal from "@/components/modals/SchoolResourcesModal";
import PasswordConfirmDeleteModal from "@/components/modals/PasswordConfirmDeleteModal";
import SchoolResourceSkeleton from "@/components/modals/SchoolResourceSkeleton";
import { verifyPassword } from "@/app/services/UserServices";
import { getClassLevelsBySchoolId } from "@/app/services/ClassLevels";

const BASE_URL = "/school-admin";

const navigation = {
  icon: BookText,
  baseHref: `${BASE_URL}/school-resources`,
  title: "School Resources",
};

// Optional: define type for class level
type ClassLevel = {
  _id: string;
  name: string;
};

function SchoolResourcesContent() {
  const [resources, setResources] = useState<SchoolResourceSchema[]>([]);
  const [loadingData, setLoadingData] = useState(true);
  const [selectedResources, setSelectedResources] = useState<SchoolResourceSchema[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [resourceToEdit, setResourceToEdit] = useState<SchoolResourceSchema | null>(null);
  const [resourceToDelete, setResourceToDelete] = useState<SchoolResourceSchema | null>(null);
  const [deleteType, setDeleteType] = useState<"single" | "multiple">("single");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<NotificationState | null>(null);
  const [clearSelection, setClearSelection] = useState(false);
  const [classLevel, setClassLevel] = useState<ClassLevel[]>([]);

  const { user } = useAuth();
  const getClassLevelName = (id: string | null | undefined): string => {
    if (!id) return "N/A";
    const found = classLevel.find(level => level._id === id);
    return found ? found.name : "Unknown";
  };

  const columns = [
    {
      header: "Resource Name",
      accessor: (row: SchoolResourceSchema) => (
        <span className="font-medium">{row.name}</span>
      ),
    },
    {
      header: "Type",
      accessor: (row: SchoolResourceSchema) => (
        <span className="text-gray-600 dark:text-gray-400">{row.type}</span>
      ),
    },
    {
      header: "Price",
      accessor: (row: SchoolResourceSchema) => (
        <span className="font-mono text-green-600 dark:text-green-400">
          ${row.price.toFixed(2)}
        </span>
      ),
    },
    {
      header: "Stock",
      accessor: (row: SchoolResourceSchema) => (
        <span className="text-blue-600 dark:text-blue-400">{row.stock}</span>
      ),
    },
    {
      header: "Class Level",
      accessor: (row: SchoolResourceSchema) => (
        <span className="text-purple-600 dark:text-purple-400">
          {getClassLevelName(row.class_level)}
        </span>
      ),
    }
  ];

  const actions = [
    {
      label: "Edit",
      onClick: (resource: SchoolResourceSchema) => {
        handleEditResource(resource);
      },
    },
    {
      label: "Delete",
      onClick: (resource: SchoolResourceSchema) => {
        handleDeleteResource(resource);
      },
    },
  ];

  useEffect(() => {
    fetchResources();
    fetchLevels(); // ✅ Fetch class levels too
  }, []);

  const fetchResources = async () => {
    try {
      setLoadingData(true);
      if (user?.school_ids && user.school_ids.length > 0) {
        const schoolId = user.school_ids[0];
        const resourcesData = await getSchoolResourcesBySchoolId(schoolId);
        setResources(resourcesData);
      } else {
        const resourcesData = await getSchoolResources();
        setResources(resourcesData);
        setSubmitStatus(createErrorNotification("No school ID found for user, fetching all resources."));
      }
    } catch (error) {
      console.error("Error fetching school resources:", error);
      setSubmitStatus(createErrorNotification("Failed to fetch school resources"));
    } finally {
      setLoadingData(false);
    }
  };

  const fetchLevels = async () => {
    try {
      if (user?.school_ids && user.school_ids.length > 0) {
        const schoolId = user.school_ids[0];
        const levels = await getClassLevelsBySchoolId(schoolId);
        setClassLevel(levels);
      }
    } catch (error) {
      console.error("Error fetching class levels:", error);
    }
  };

  const handleCreateResource = () => {
    setResourceToEdit(null);
    setIsModalOpen(true);
  };

  const handleEditResource = (resource: SchoolResourceSchema) => {
    setResourceToEdit(resource);
    setIsModalOpen(true);
  };

  const handleDeleteResource = (resource: SchoolResourceSchema) => {
    setResourceToDelete(resource);
    setDeleteType("single");
    setIsDeleteModalOpen(true);
  };

  const handleDeleteMultiple = (selectedIds: string[]) => {
    setDeleteType("multiple");
    setResourceToDelete(null);
    setIsDeleteModalOpen(true);
  };

  const handleSelectionChange = (selected: SchoolResourceSchema[]) => {
    setSelectedResources(selected);
  };

  const handleSave = async (resourceData: any) => {
    setIsSubmitting(true);
    try {
      if (resourceToEdit) {
        await updateSchoolResource(resourceToEdit._id, resourceData);
        setSubmitStatus(createSuccessNotification("School resource updated successfully"));
      } else {
        const newResourceData = { ...resourceData, school_id: user?.school_ids?.[0] };
        await createSchoolResource(newResourceData);
        setSubmitStatus(createSuccessNotification("School resource created successfully"));
      }

      setIsModalOpen(false);
      setResourceToEdit(null);
      await fetchResources();
    } catch (error) {
      console.error("Error saving school resource:", error);
      setSubmitStatus(createErrorNotification("Failed to save school resource"));
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteConfirm = async (password: string) => {
    setIsSubmitting(true);
    try {
      if (!user?.email) throw new Error("User email not found");

      await verifyPassword(user.email, password);

      if (deleteType === "single" && resourceToDelete) {
        await deleteSchoolResource(resourceToDelete._id);
        setSubmitStatus(createSuccessNotification("School resource deleted successfully"));
      } else if (deleteType === "multiple" && selectedResources.length > 0) {
        const ids = selectedResources.map(resource => resource._id);
        await deleteMultipleSchoolResources(ids);
        setSubmitStatus(createSuccessNotification(`${selectedResources.length} resources deleted`));
        setSelectedResources([]);
        setClearSelection(true);
      }

      setIsDeleteModalOpen(false);
      setResourceToDelete(null);
      await fetchResources();
    } catch (error) {
      console.error("Error deleting school resource(s):", error);
      if (error instanceof Error && error.message.includes("password")) {
        setSubmitStatus(createErrorNotification("Invalid password"));
      } else {
        setSubmitStatus(createErrorNotification("Failed to delete school resource(s)"));
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loadingData) {
    return <SchoolResourceSkeleton />;
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4 text-text">School Resources Management</h1>

      {submitStatus && (
        <div className="mb-4">
          <NotificationCard
            type={submitStatus.type}
            title={submitStatus.title}
            message={submitStatus.message}
            onClose={() => setSubmitStatus(null)}
            isVisible={true}
          />
        </div>
      )}

      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        transition={{ type: "spring", stiffness: 300 }}
        onClick={handleCreateResource}
        className="mb-4 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors"
      >
        Add New School Resource
      </motion.button>

      <DataTableFix<SchoolResourceSchema>
        data={resources}
        columns={columns}
        actions={actions}
        defaultItemsPerPage={10}
        onSelectionChange={handleSelectionChange}
        handleDeleteMultiple={handleDeleteMultiple}
        clearSelection={clearSelection}
        onSelectionCleared={() => setClearSelection(false)}
      />

      <SchoolResourceModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setResourceToEdit(null);
        }}
        onSave={handleSave}
        resource={resourceToEdit}
        isSubmitting={isSubmitting}
        classLevels={classLevel} // ✅ Pass to modal
      />

      <PasswordConfirmDeleteModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setResourceToDelete(null);
        }}
        onConfirm={handleDeleteConfirm}
        title={
          deleteType === "single"
            ? "Delete School Resource"
            : "Delete Selected School Resources"
        }
        message={
          deleteType === "single"
            ? "Are you sure you want to delete this school resource? This action cannot be undone."
            : `Are you sure you want to delete ${selectedResources.length} selected school resources? This action cannot be undone.`
        }
        itemName={deleteType === "single" && resourceToDelete ? resourceToDelete.name : undefined}
        itemCount={deleteType === "multiple" ? selectedResources.length : undefined}
        type={deleteType}
      />
    </div>
  );
}

export default function Page() {
  const { logout } = useAuth();

  return (
    <Suspense
      fallback={
        <div className="flex justify-center items-center h-screen absolute top-0 left-0 z-50">
          <CircularLoader size={32} color="teal" />
        </div>
      }
    >
      <SchoolLayout navigation={navigation} showGoPro={true} onLogout={() => logout()}>
        <SchoolResourcesContent />
      </SchoolLayout>
    </Suspense>
  );
}
