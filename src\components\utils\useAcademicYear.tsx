import { useState, useEffect } from 'react';
import { getAcademicYears } from '@/app/services/AcademicYearServices';
import { AcademicYearSchema } from '@/app/models/AcademicYear';
import useAuth from '@/app/hooks/useAuth';

const MAX_RETRIES = 3;
const RETRY_DELAY = 1500; // milliseconds

const useAcademicYear = () => {
  const {user} = useAuth();
  const [currentAcademicYear, setCurrentAcademicYear] = useState<string>('');
  const [yearLoading, setYearLoading] = useState(false);
  const [allAcademicYears, setAllAcademicYears] = useState<AcademicYearSchema[]>([]);

  const getCurrentAcademicYear = (academicYears: AcademicYearSchema[]): string => {
    const normalize = (dateStr: string | Date) => {
      const d = new Date(dateStr);
      d.setHours(0, 0, 0, 0);
      return d;
    };

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const sortedAcademicYears = academicYears.sort(
      (a, b) => new Date(b.start_date).getTime() - new Date(a.start_date).getTime()
    );

    const isWithinGracePeriod = today.getMonth() === 6 || today.getMonth() === 7;

    const current = academicYears.find((year) => {
      const start = normalize(year.start_date);
      const end = normalize(year.end_date);

      if (isWithinGracePeriod) {
        return today <= end;
      }

      return today >= start && today <= end;
    });

    return current?.academic_year || sortedAcademicYears[0]?.academic_year || '';
  };

  const fetchAcademicYear = async (retry = 0): Promise<void> => {
    try {
      setYearLoading(true);
      const years = await getAcademicYears();
      setAllAcademicYears(years);
      console.log('📅 Fetched academic years:', years);

      const current = getCurrentAcademicYear(years);
      if (current) {
        setCurrentAcademicYear(current);
        console.log('✅ Academic Year set:', current);
      } else if (retry < MAX_RETRIES) {
        console.warn(`⚠️ Retry ${retry + 1} - Academic year not found, retrying in ${RETRY_DELAY}ms...`);
        setTimeout(() => fetchAcademicYear(retry + 1), RETRY_DELAY);
      } else {
        console.error('❌ Failed to get a valid academic year after retries.');
      }
    } catch (error) {
      console.error('❌ Error fetching academic years:', error);
      if (retry < MAX_RETRIES) {
        setTimeout(() => fetchAcademicYear(retry + 1), RETRY_DELAY);
      }
    } finally {
      setYearLoading(false);
    }
  };

  useEffect(() => {
    if(!user) return;
    fetchAcademicYear();
  }, [user]);

  return {
    currentAcademicYear,
    yearLoading,
    refreshAcademicYear: fetchAcademicYear,
    allAcademicYears,
  };
};

export default useAcademicYear;
