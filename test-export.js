// Script de test pour les fonctionnalités d'export
const mongoose = require('mongoose');
require('dotenv').config();

// Connexion à la base de données
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/scholarify', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

// Import des modèles
const SchoolSubscription = require('./src/models/SchoolSubscription');
const CreditPurchase = require('./src/models/CreditPurchase');
const School = require('./src/models/School');
const CreditUsage = require('./src/models/CreditUsage');

// Fonction pour créer des données de test
async function createTestData() {
  try {
    console.log('🔄 Création des données de test...');

    // Créer une école de test
    const testSchool = await School.findOneAndUpdate(
      { name: 'École de Test Export' },
      {
        name: 'École de Test Export',
        email: '<EMAIL>',
        address: '123 Rue de Test',
        phone: '+237123456789'
      },
      { upsert: true, new: true }
    );

    console.log('✅ École de test créée:', testSchool.name);

    // Créer une souscription de test
    const testSubscription = await SchoolSubscription.findOneAndUpdate(
      { school_id: testSchool._id },
      {
        school_id: testSchool._id,
        plan_type: 'standard',
        status: 'active',
        credits_balance: 150,
        credits_purchased: 500,
        credits_used: 350,
        features: ['student_management', 'class_management', 'chatbot_access']
      },
      { upsert: true, new: true }
    );

    console.log('✅ Souscription de test créée:', testSubscription.plan_type);

    // Créer des achats de crédits de test
    const testPurchases = [];
    for (let i = 0; i < 5; i++) {
      const purchase = await CreditPurchase.findOneAndUpdate(
        { 
          school_id: testSchool._id,
          purchase_date: new Date(Date.now() - (i * 7 * 24 * 60 * 60 * 1000)) // Une semaine d'écart
        },
        {
          school_id: testSchool._id,
          credits_amount: 100,
          unit_price: 3000,
          total_amount: 300000,
          payment_status: 'completed',
          purchase_date: new Date(Date.now() - (i * 7 * 24 * 60 * 60 * 1000)),
          payment_method: 'fapshi',
          transaction_id: `TEST_${Date.now()}_${i}`
        },
        { upsert: true, new: true }
      );
      testPurchases.push(purchase);
    }

    console.log('✅ Achats de test créés:', testPurchases.length);

    // Créer des utilisations de crédits de test
    const testUsages = [];
    for (let i = 0; i < 10; i++) {
      const usage = await CreditUsage.create({
        school_id: testSchool._id,
        credits_used: Math.floor(Math.random() * 10) + 1,
        usage_type: 'student_creation',
        description: `Création étudiant test ${i + 1}`,
        usage_date: new Date(Date.now() - (i * 2 * 24 * 60 * 60 * 1000)), // Tous les 2 jours
        used_by: testSchool._id,
        status: 'completed'
      });
      testUsages.push(usage);
    }

    console.log('✅ Utilisations de test créées:', testUsages.length);

    return {
      school: testSchool,
      subscription: testSubscription,
      purchases: testPurchases,
      usages: testUsages
    };

  } catch (error) {
    console.error('❌ Erreur lors de la création des données de test:', error);
    throw error;
  }
}

// Fonction pour tester l'export global
async function testGlobalExport() {
  try {
    console.log('\n🔄 Test de l\'export global...');

    // Import des fonctions d'export
    const { exportGlobalReport } = require('./src/controllers/schoolSubscriptionController');

    // Mock de la réponse
    const mockRes = {
      setHeader: (key, value) => console.log(`Header: ${key} = ${value}`),
      send: (data) => console.log('✅ Export global réussi, taille:', data.length, 'bytes'),
      status: (code) => ({ json: (data) => console.log(`Status ${code}:`, data) })
    };

    // Mock de la requête
    const mockReq = {
      query: {
        format: 'pdf',
        period: 'month'
      }
    };

    await exportGlobalReport(mockReq, mockRes);

  } catch (error) {
    console.error('❌ Erreur lors du test d\'export global:', error);
  }
}

// Fonction pour tester l'export d'école
async function testSchoolExport(schoolId) {
  try {
    console.log('\n🔄 Test de l\'export d\'école...');

    // Import des fonctions d'export
    const { exportSchoolReport } = require('./src/controllers/schoolSubscriptionController');

    // Mock de la réponse
    const mockRes = {
      setHeader: (key, value) => console.log(`Header: ${key} = ${value}`),
      send: (data) => console.log('✅ Export école réussi, taille:', data.length, 'bytes'),
      status: (code) => ({ json: (data) => console.log(`Status ${code}:`, data) })
    };

    // Mock de la requête
    const mockReq = {
      params: { school_id: schoolId },
      query: {
        format: 'excel',
        period: 'month'
      }
    };

    await exportSchoolReport(mockReq, mockRes);

  } catch (error) {
    console.error('❌ Erreur lors du test d\'export d\'école:', error);
  }
}

// Fonction principale de test
async function runTests() {
  try {
    console.log('🚀 Démarrage des tests d\'export...\n');

    // Créer les données de test
    const testData = await createTestData();

    // Tester l'export global
    await testGlobalExport();

    // Tester l'export d'école
    await testSchoolExport(testData.school._id);

    console.log('\n✅ Tous les tests sont terminés !');

  } catch (error) {
    console.error('❌ Erreur lors des tests:', error);
  } finally {
    // Fermer la connexion
    await mongoose.connection.close();
    console.log('🔌 Connexion fermée');
  }
}

// Exécuter les tests si le script est appelé directement
if (require.main === module) {
  runTests();
}

module.exports = {
  createTestData,
  testGlobalExport,
  testSchoolExport,
  runTests
};
