# 📋 Guide de Gestion des Plans de Souscription

## 🎯 Vue d'ensemble

Cette nouvelle fonctionnalité permet aux Super Admins de créer, modifier et gérer les plans de souscription directement depuis l'interface d'administration.

## 🚀 Accès à la Fonctionnalité

### Navigation
1. Connectez-vous en tant que **Super Admin**
2. Dans le menu latéral, cliquez sur **"Plans Souscription"**
3. Vous accédez à la page de gestion : `/super-admin/subscription-plans`

## 📊 Interface de Gestion

### Vue d'ensemble
- **Statistiques** : Total des plans, plans actifs, populaires, avec chatbot
- **Filtres** : Recherche par nom/description, filtrage par statut
- **Actions** : <PERSON><PERSON><PERSON>, modifier, supprimer des plans

### Cartes de Plans
Chaque plan affiche :
- **Nom d'affichage** et nom technique
- **Prix par crédit** (ou "Sur mesure" si 0)
- **Limites d'achat** (minimum/maximum)
- **Statut** (actif/inactif)
- **Badge "Populaire"** si applicable
- **Nombre de fonctionnalités**
- **Support chatbot**

## ✨ Créer un Nouveau Plan

### 1. Cliquer sur "Nouveau Plan"
### 2. Remplir le Formulaire

#### **Informations de Base**
- **Nom d'affichage*** : Nom visible par les clients (ex: "Plan Basic")
- **Nom technique** : Identifiant unique (auto-généré si vide)
- **Description*** : Description détaillée du plan

#### **Tarification**
- **Prix par crédit*** : Prix en XAF (0 = sur mesure)
- **Achat minimum*** : Nombre minimum de crédits
- **Achat maximum** : Limite maximale (optionnel)

#### **Chatbot**
- **Activer le chatbot** : Checkbox pour activer
- **Crédits chatbot par achat** : Si chatbot activé

#### **Fonctionnalités**
- **Liste des fonctionnalités** : Ajout/suppression dynamique
- **Limitations** : Restrictions du plan

#### **Options**
- **Recommandé pour*** : Type d'établissement ciblé
- **Ordre d'affichage*** : Position dans la liste
- **Plan actif** : Visible aux clients
- **Plan populaire** : Badge spécial
- **Contact requis** : Nécessite un contact commercial

### 3. Validation et Sauvegarde
Le système valide automatiquement :
- Champs obligatoires
- Format des données
- Cohérence des valeurs

## 🔧 Modifier un Plan Existant

1. **Cliquer sur l'icône "Modifier"** sur la carte du plan
2. **Modifier les informations** dans le formulaire pré-rempli
3. **Sauvegarder** les modifications

## 🗑️ Supprimer un Plan

1. **Cliquer sur l'icône "Supprimer"** (poubelle rouge)
2. **Confirmer** la suppression dans la popup
3. Le plan est définitivement supprimé

⚠️ **Attention** : La suppression est irréversible !

## 🔍 Recherche et Filtrage

### Recherche
- **Barre de recherche** : Recherche par nom, description
- **Temps réel** : Résultats instantanés

### Filtres
- **Tous les plans** : Affichage complet
- **Actifs** : Plans visibles aux clients
- **Inactifs** : Plans désactivés
- **Populaires** : Plans avec badge spécial

## 📈 Bonnes Pratiques

### Nommage
- **Noms clairs** : "Plan Basic", "Plan Premium", "Plan Enterprise"
- **Descriptions détaillées** : Expliquer clairement les avantages
- **Ordre logique** : Du moins cher au plus cher

### Tarification
- **Prix cohérents** : Progression logique des prix
- **Minimums raisonnables** : Pas trop élevés pour débuter
- **Maximums adaptés** : Selon la capacité des écoles

### Fonctionnalités
- **Listes claires** : Fonctionnalités faciles à comprendre
- **Différenciation** : Chaque plan doit avoir sa valeur unique
- **Limitations explicites** : Être transparent sur les restrictions

## 🔄 Intégration avec les Remboursements

### Service RefundServices
Le système de remboursement a été amélioré avec :
- **Service dédié** : `RefundServices.tsx`
- **Gestion centralisée** : Toutes les API calls dans le service
- **Types TypeScript** : Interfaces bien définies
- **Validation** : Contrôles de données intégrés

### Fonctions Disponibles
```typescript
// Récupérer les transactions problématiques
getProblematicTransactions(): Promise<ProblematicTransaction[]>

// Effectuer un remboursement
processRefund(refundData: RefundRequest): Promise<RefundResponse>

// Statistiques de surveillance
getMonitoringStats(): Promise<MonitoringStats>

// Vérifier le statut d'un paiement
checkPaymentStatus(transactionId: string): Promise<any>
```

## 🛠️ Architecture Technique

### Services
- **SubscriptionPlanServices.tsx** : Gestion des plans
- **RefundServices.tsx** : Gestion des remboursements

### Composants
- **SubscriptionPlansPage** : Page principale
- **SubscriptionPlanModal** : Modal de création/modification
- **RefundModal** : Modal de remboursement

### Navigation
- **SuperLayout** : Navigation mise à jour
- **Routes** : Nouvelles routes ajoutées

## 🔐 Sécurité

### Authentification
- **Token Firebase** : Requis pour toutes les opérations
- **Rôles** : Seuls les Super Admins ont accès
- **Validation** : Contrôles côté client et serveur

### Validation des Données
- **Champs obligatoires** : Vérification automatique
- **Types de données** : Validation TypeScript
- **Cohérence** : Contrôles métier (min < max, etc.)

## 📝 Notes Importantes

### Limitations Actuelles
- **Suppression** : Pas de vérification des plans en cours d'utilisation
- **Historique** : Pas de suivi des modifications
- **Permissions** : Accès limité aux Super Admins uniquement

### Améliorations Futures
- **Audit trail** : Historique des modifications
- **Permissions granulaires** : Accès par rôle
- **Validation avancée** : Vérification des plans actifs
- **Import/Export** : Sauvegarde et restauration

## 🆘 Dépannage

### Problèmes Courants

1. **"No authentication token found"**
   - Vérifier la connexion
   - Recharger la page

2. **"Failed to fetch subscription plans"**
   - Vérifier la connexion réseau
   - Contrôler les logs serveur

3. **Erreurs de validation**
   - Vérifier les champs obligatoires
   - Respecter les formats requis

### Support
En cas de problème :
1. Vérifier les logs de la console navigateur
2. Contrôler les logs du serveur backend
3. Tester avec des données simples
4. Contacter l'équipe technique

---

**Version** : 1.0  
**Dernière mise à jour** : Décembre 2024  
**Compatibilité** : React 18+, TypeScript 5+
