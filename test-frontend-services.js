/**
 * Script de test pour les services frontend
 * 
 * Ce script teste les services frontend pour s'assurer qu'ils gèrent
 * correctement les erreurs et les cas limites.
 * 
 * Usage: node test-frontend-services.js
 */

const fetch = require('node-fetch');

// Configuration de test
const BASE_API_URL = 'https://scolarify.onrender.com/api';
const TEST_TOKEN = 'test_token_123'; // Token de test

// Fonction utilitaire pour simuler les appels API
async function testApiCall(endpoint, options = {}) {
  try {
    console.log(`🔍 Testing: ${endpoint}`);
    
    const response = await fetch(`${BASE_API_URL}${endpoint}`, {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${TEST_TOKEN}`,
        ...options.headers
      },
      body: options.body ? JSON.stringify(options.body) : undefined
    });

    const status = response.status;
    let data;
    
    try {
      data = await response.json();
    } catch (e) {
      data = { error: 'Failed to parse JSO<PERSON>' };
    }

    console.log(`   Status: ${status}`);
    console.log(`   Response: ${JSON.stringify(data).substring(0, 100)}...`);
    
    return { status, data, ok: response.ok };
  } catch (error) {
    console.log(`   Error: ${error.message}`);
    return { status: 0, data: null, ok: false, error: error.message };
  }
}

async function testServices() {
  console.log('🧪 Test des Services Frontend\n');
  console.log('================================\n');

  // 1. Test du service RefundServices
  console.log('1️⃣ Test RefundServices');
  console.log('----------------------');
  
  // Test getProblematicTransactions
  const refundTest = await testApiCall('/credit-purchase/problematic');
  
  if (refundTest.status === 404) {
    console.log('   ✅ 404 géré correctement - devrait retourner tableau vide');
  } else if (refundTest.status === 401) {
    console.log('   ✅ 401 géré correctement - token invalide');
  } else if (refundTest.ok && Array.isArray(refundTest.data)) {
    console.log('   ✅ Données reçues correctement');
  } else {
    console.log('   ⚠️ Réponse inattendue');
  }
  
  console.log('');

  // 2. Test du service SubscriptionPlanServices
  console.log('2️⃣ Test SubscriptionPlanServices');
  console.log('--------------------------------');
  
  // Test getAllSubscriptionPlans
  const plansTest = await testApiCall('/subscription-plans');
  
  if (plansTest.ok && plansTest.data && plansTest.data.plans) {
    console.log('   ✅ Plans récupérés avec structure { plans: [...] }');
  } else if (plansTest.ok && Array.isArray(plansTest.data)) {
    console.log('   ✅ Plans récupérés comme tableau direct');
  } else if (plansTest.status === 404) {
    console.log('   ✅ 404 géré correctement - devrait retourner tableau vide');
  } else {
    console.log('   ⚠️ Réponse inattendue');
  }
  
  console.log('');

  // 3. Test des endpoints CRUD pour les plans
  console.log('3️⃣ Test CRUD Plans (nécessite authentification)');
  console.log('-----------------------------------------------');
  
  // Test création de plan (devrait échouer sans auth valide)
  const createTest = await testApiCall('/subscription-plans', {
    method: 'POST',
    body: {
      plan_name: 'test_plan',
      display_name: 'Plan Test',
      description: 'Plan de test',
      price_per_credit: 1000,
      minimum_purchase: 1,
      chatbot_enabled: false,
      chatbot_credits_per_purchase: 0,
      features: ['Test feature'],
      limitations: ['Test limitation'],
      recommended_for: 'Test',
      is_active: true,
      is_popular: false,
      sort_order: 1,
      contact_required: false
    }
  });
  
  if (createTest.status === 401 || createTest.status === 403) {
    console.log('   ✅ Authentification requise pour création');
  } else {
    console.log('   ⚠️ Création sans auth - vérifier sécurité');
  }
  
  console.log('');

  // 4. Test de gestion d'erreurs réseau
  console.log('4️⃣ Test Gestion Erreurs Réseau');
  console.log('------------------------------');
  
  // Test avec endpoint inexistant
  const notFoundTest = await testApiCall('/endpoint-inexistant');
  
  if (notFoundTest.status === 404) {
    console.log('   ✅ 404 géré correctement');
  } else {
    console.log('   ⚠️ Gestion 404 inattendue');
  }
  
  console.log('');

  // 5. Résumé des recommandations
  console.log('📋 RÉSUMÉ ET RECOMMANDATIONS');
  console.log('============================');
  
  console.log('✅ Services configurés pour gérer:');
  console.log('   - Erreurs 404 → tableau vide');
  console.log('   - Erreurs réseau → tableau vide');
  console.log('   - Formats de données variables');
  console.log('   - Authentification manquante');
  
  console.log('');
  console.log('🔧 Corrections appliquées:');
  console.log('   - RefundServices: gestion 404 et erreurs réseau');
  console.log('   - SubscriptionPlanServices: extraction data.plans');
  console.log('   - Validation Array.isArray() partout');
  console.log('   - Fallback vers tableaux vides');
  
  console.log('');
  console.log('🚀 Prochaines étapes:');
  console.log('   1. Tester avec un token valide');
  console.log('   2. Vérifier les endpoints CRUD en production');
  console.log('   3. Initialiser des plans par défaut si nécessaire');
  console.log('   4. Tester l\'interface utilisateur');
}

// Fonction pour tester la logique de validation côté frontend
function testValidationLogic() {
  console.log('');
  console.log('5️⃣ Test Logique de Validation');
  console.log('-----------------------------');
  
  // Test validation numéro de téléphone
  const phoneRegex = /^6[0-9]{8}$/;
  const phoneTests = [
    { phone: '677123456', expected: true },
    { phone: '123456789', expected: false },
    { phone: '67712345', expected: false },
    { phone: '6771234567', expected: false },
    { phone: '', expected: false }
  ];
  
  console.log('   Validation téléphone:');
  phoneTests.forEach(test => {
    const result = phoneRegex.test(test.phone);
    const status = result === test.expected ? '✅' : '❌';
    console.log(`     ${status} "${test.phone}" → ${result} (attendu: ${test.expected})`);
  });
  
  // Test validation plan de souscription
  console.log('   Validation plan:');
  const planTests = [
    {
      plan: { display_name: 'Test', description: 'Test description long', price_per_credit: 1000 },
      expectedErrors: 0
    },
    {
      plan: { display_name: '', description: 'Test', price_per_credit: -1 },
      expectedErrors: 2
    }
  ];
  
  planTests.forEach((test, index) => {
    let errors = 0;
    if (!test.plan.display_name || test.plan.display_name.length < 2) errors++;
    if (!test.plan.description || test.plan.description.length < 10) errors++;
    if (test.plan.price_per_credit < 0) errors++;
    
    const status = errors === test.expectedErrors ? '✅' : '❌';
    console.log(`     ${status} Plan ${index + 1}: ${errors} erreurs (attendu: ${test.expectedErrors})`);
  });
}

// Exécuter les tests
async function runAllTests() {
  await testServices();
  testValidationLogic();
  
  console.log('');
  console.log('🎉 Tests terminés !');
  console.log('');
  console.log('💡 Pour tester avec de vraies données:');
  console.log('   1. Remplacez TEST_TOKEN par un vrai token');
  console.log('   2. Connectez-vous en tant que Super Admin');
  console.log('   3. Testez les fonctionnalités dans l\'interface');
}

// Exécuter si appelé directement
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = { testServices, testValidationLogic };
