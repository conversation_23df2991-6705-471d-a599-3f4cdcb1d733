"use client";

import SuperLayout from '@/components/Dashboard/Layouts/SuperLayout';
import CircularLoader from '@/components/widgets/CircularLoader';
import React, { Suspense, useEffect, useState, useMemo } from 'react';
import {
  Coins,
  Search,
  MapPin,
  Building2,
  MoveRight,
  TrendingUp,
  Users,
  DollarSign,
  Calendar,
  CreditCard,
  BarChart3,
  Target,
  Clock
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { getSchools } from '@/app/services/SchoolServices';
import { SchoolSchema } from '@/app/models/SchoolModel';
import {
  getSchoolCompleteAnalytics,
  formatCurrency,
  formatCredits,
  getPlanTypeText,
  getPlanTypeColor,
  SchoolCompleteAnalyticsResponse
} from '@/app/services/CreditPurchaseServices';
import { motion } from 'framer-motion';

export default function Page() {
    const BASE_URL = "/super-admin";

    const navigation = {
        icon: Coins,
        baseHref: `${BASE_URL}/credit`,
        title: "Gestion des Crédits et Analytics",
    };

    function Credit() {
        const router = useRouter();
        const [schools, setSchools] = useState<SchoolSchema[]>([]);
        const [schoolsAnalytics, setSchoolsAnalytics] = useState<Map<string, SchoolCompleteAnalyticsResponse>>(new Map());
        const [loadingData, setLoadingData] = useState(false);
        const [loadingAnalytics, setLoadingAnalytics] = useState(false);
        const [searchTerm, setSearchTerm] = useState('');

        // Fetch schools data
        useEffect(() => {
            const fetchSchools = async () => {
                setLoadingData(true);
                try {
                    const schoolsData = await getSchools();
                    setSchools(schoolsData);
                } catch (error) {
                    console.error("Error fetching schools:", error);
                } finally {
                    setLoadingData(false);
                }
            };

            fetchSchools();
        }, []);

        // Fetch analytics for all schools
        useEffect(() => {
            const fetchAnalytics = async () => {
                if (schools.length === 0) return;

                setLoadingAnalytics(true);
                const analyticsMap = new Map<string, SchoolCompleteAnalyticsResponse>();

                try {
                    const analyticsPromises = schools.map(async (school) => {
                        try {
                            const analytics = await getSchoolCompleteAnalytics(school._id);
                            analyticsMap.set(school._id, analytics);
                        } catch (error) {
                            console.error(`Error fetching analytics for school ${school.name}:`, error);
                        }
                    });

                    await Promise.all(analyticsPromises);
                    setSchoolsAnalytics(analyticsMap);
                } catch (error) {
                    console.error("Error fetching analytics:", error);
                } finally {
                    setLoadingAnalytics(false);
                }
            };

            fetchAnalytics();
        }, [schools]);

        // Filter schools based on search term
        const filteredSchools = useMemo(() => {
            const lowercasedSearchTerm = searchTerm.toLowerCase();
            return schools.filter(school =>
                school.name.toLowerCase().includes(lowercasedSearchTerm) ||
                (school.address ?? "").toLowerCase().includes(lowercasedSearchTerm)
            );
        }, [schools, searchTerm]);

        return (
            <div className="p-6 space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                            Gestion des Crédits et Analytics
                        </h1>
                        <p className="text-gray-600 dark:text-gray-400 mt-2">
                            Vue d'ensemble des achats de crédits et analyses par école
                        </p>
                    </div>
                </div>

                {/* Search Bar */}
                <div className="relative">
                    <input
                        type="text"
                        placeholder="Rechercher une école par nom ou adresse..."
                        className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                </div>

                {loadingData ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {/* Loading Skeletons */}
                        {[...Array(6)].map((_, index) => (
                            <div key={index} className="bg-white dark:bg-gray-700 rounded-lg shadow-md p-6 animate-pulse">
                                <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-3/4 mb-4"></div>
                                <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2 mb-2"></div>
                                <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-2/3 mb-6"></div>
                                <div className="h-10 bg-gray-300 dark:bg-gray-600 rounded w-full"></div>
                            </div>
                        ))}
                    </div>
                ) : filteredSchools.length === 0 ? (
                    <div className="text-center py-10 text-gray-500 dark:text-gray-400">
                        <p>No schools found matching your search criteria.</p>
                    </div>
                ) : (
                    <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                        {filteredSchools.map((school) => {
                            const analytics = schoolsAnalytics.get(school._id);

                            return (
                                <motion.div
                                    key={school._id}
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-300"
                                >
                                    {/* School Header */}
                                    <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center space-x-3">
                                                <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                                                    <Building2 className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                                                </div>
                                                <div>
                                                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                                                        {school.name}
                                                    </h3>
                                                    <p className="text-sm text-gray-600 dark:text-gray-400 flex items-center">
                                                        <MapPin className="h-4 w-4 mr-1" />
                                                        {school.address}
                                                    </p>
                                                </div>
                                            </div>
                                            {analytics?.subscription && (
                                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPlanTypeColor(analytics.subscription.plan_type)}`}>
                                                    {getPlanTypeText(analytics.subscription.plan_type)}
                                                </span>
                                            )}
                                        </div>
                                    </div>

                                    {/* Analytics Content */}
                                    {loadingAnalytics ? (
                                        <div className="p-6 flex justify-center">
                                            <CircularLoader />
                                        </div>
                                    ) : analytics ? (
                                        <div className="p-6 space-y-4">
                                            {/* Key Metrics */}
                                            <div className="grid grid-cols-2 gap-4">
                                                <div className="text-center">
                                                    <div className="flex items-center justify-center mb-1">
                                                        <CreditCard className="h-4 w-4 text-green-500 mr-1" />
                                                        <span className="text-sm text-gray-600 dark:text-gray-400">Solde</span>
                                                    </div>
                                                    <p className="text-xl font-bold text-green-600 dark:text-green-400">
                                                        {formatCredits(analytics.analytics.current_balance)}
                                                    </p>
                                                </div>
                                                <div className="text-center">
                                                    <div className="flex items-center justify-center mb-1">
                                                        <DollarSign className="h-4 w-4 text-blue-500 mr-1" />
                                                        <span className="text-sm text-gray-600 dark:text-gray-400">Revenus</span>
                                                    </div>
                                                    <p className="text-xl font-bold text-blue-600 dark:text-blue-400">
                                                        {formatCurrency(analytics.analytics.total_revenue)}
                                                    </p>
                                                </div>
                                            </div>

                                            {/* Additional Metrics */}
                                            <div className="grid grid-cols-3 gap-3 text-center">
                                                <div>
                                                    <div className="flex items-center justify-center mb-1">
                                                        <Target className="h-3 w-3 text-orange-500 mr-1" />
                                                    </div>
                                                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                                                        {analytics.analytics.efficiency}%
                                                    </p>
                                                    <p className="text-xs text-gray-600 dark:text-gray-400">Efficacité</p>
                                                </div>
                                                <div>
                                                    <div className="flex items-center justify-center mb-1">
                                                        <Users className="h-3 w-3 text-purple-500 mr-1" />
                                                    </div>
                                                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                                                        {analytics.analytics.total_used}
                                                    </p>
                                                    <p className="text-xs text-gray-600 dark:text-gray-400">Étudiants</p>
                                                </div>
                                                <div>
                                                    <div className="flex items-center justify-center mb-1">
                                                        <Clock className="h-3 w-3 text-red-500 mr-1" />
                                                    </div>
                                                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                                                        {analytics.analytics.days_remaining}j
                                                    </p>
                                                    <p className="text-xs text-gray-600 dark:text-gray-400">Restants</p>
                                                </div>
                                            </div>

                                            {/* ARPU */}
                                            <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
                                                <div className="flex items-center justify-between">
                                                    <span className="text-sm text-gray-600 dark:text-gray-400">ARPU</span>
                                                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                                                        {formatCurrency(analytics.analytics.arpu)}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="p-6 text-center text-gray-500 dark:text-gray-400">
                                            <BarChart3 className="h-8 w-8 mx-auto mb-2 opacity-50" />
                                            <p className="text-sm">Aucune donnée disponible</p>
                                        </div>
                                    )}

                                    {/* Action Button */}
                                    <div className="p-6 pt-0">
                                        <button
                                            onClick={() => router.push(`${BASE_URL}/credit/manage?id=${school._id}`)}
                                            className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                                        >
                                            <BarChart3 className="h-4 w-4 mr-2" />
                                            Voir Détails
                                            <MoveRight className="h-4 w-4 ml-2" />
                                        </button>
                                    </div>
                                </motion.div>
                            );
                        })}
                    </div>
                )}
            </div>
        );
    }

    return (
        <Suspense fallback={
            <div className="flex justify-center items-center h-screen w-screen fixed top-0 left-0 bg-gray-50 dark:bg-gray-900 z-50">
                <CircularLoader size={32} color="teal" />
            </div>
        }>
            <SuperLayout
                navigation={navigation}
                showGoPro={true}
                onLogout={() => console.log("Logged out")}
            >
                <Credit />
            </SuperLayout>
        </Suspense>
    );
}