export interface RegistrationDraftSchema extends Record<string, unknown> {
    _id: string;                    // MongoDB ObjectId as string
    user_id: string;                // MongoDB ObjectId of the User
    school_id: string;              // MongoDB ObjectId of the School
    currentStep: number;            // Current step in the registration process
    isCompleted: boolean;           // Whether the registration draft is completed
    status: 'not enrolled' | 'pending payment' | 'enrolled' | 'draft';  // Status of the draft
    firstName: string;              // Student's first name
    lastName: string;               // Student's last name
    middleName: string;             // Student's middle name (optional)
    dateOfBirth: Date | null;       // Student's date of birth
    nationality: string;            // Student's nationality
    gender: 'Male' | 'Female' | 'Other';  // Gender of the student
    place_of_birth: string;         // Place of birth
    address: string;                // Student's address
    student_phone: string;          // Student's phone number
    student_country_code: string;   // Country code for student's phone
    guardian_address: string;       // Guardian's address
    guardian_phone: string;         // Guardian's phone number
    guardian_country_code: string;  // Country code for guardian's phone
    guardian_name: string;          // Guardian's name
    guardian_occupation: string;    // Guardian's occupation
    guardian_email: string;         // <PERSON>'s email address
    guardian_relationship: string;  // Guardian's relationship to the student
    emergency_contact_name: string; // Emergency contact name
    emergency_contact_phone: string; // Emergency contact phone
    emergency_contact_country_code: string;  // Emergency contact phone country code
    emergency_contact_relationship: string; // Emergency contact relationship to student
    previous_school: string;        // Previous school attended by the student
    class_level: string;            // Class level of the student
    guardian_agreed_to_terms: boolean;  // Whether the guardian agreed to the terms
    transcript_reportcard: boolean;  // Whether the transcript/report card is available
    health_condition: string;       // Health conditions (if any)
    doctors_name: string;           // Doctor's name
    doctors_phone: string;          // Doctor's phone number
    doctor_country_code: string;    // Doctor's phone country code
    registered: boolean;            // Whether the student is registered
    selectedFees: string[];         // List of selected fees
    selectedResources: string[];    // List of selected resources
    paymentMode: 'full' | 'installment'; // Payment mode selected by the user
    installments: number;           // Number of installments if payment mode is 'installment'
    installmentDates: Date[];       // Dates for the installments
    applyScholarship: boolean;      // Whether the student applied for a scholarship
    scholarshipAmount: number;     // Scholarship amount awarded
    scholarshipPercentage: number; // Scholarship percentage awarded
    createdAt: string;              // Document creation timestamp (auto-generated by MongoDB)
    updatedAt: string;              // Document last updated timestamp (auto-generated by MongoDB)
}
