/**
 * Test rapide de l'endpoint problematic transactions
 */

const fetch = require('node-fetch');

async function testEndpoint() {
  try {
    console.log('🔍 Testing /api/credit-purchase/problematic endpoint...');
    
    const response = await fetch('http://localhost:3002/api/credit-purchase/problematic', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test_token' // Token de test
      }
    });

    console.log(`📡 Status: ${response.status}`);
    console.log(`📡 Status Text: ${response.statusText}`);
    
    const responseText = await response.text();
    console.log(`📡 Response: ${responseText.substring(0, 500)}...`);
    
    if (response.ok) {
      try {
        const data = JSON.parse(responseText);
        console.log(`✅ Success: ${Array.isArray(data) ? data.length : 'Not array'} transactions`);
      } catch (e) {
        console.log('⚠️ Response is not valid JSON');
      }
    } else {
      console.log(`❌ Error: ${response.status} - ${responseText}`);
    }
    
  } catch (error) {
    console.log(`❌ Request failed: ${error.message}`);
  }
}

testEndpoint();
