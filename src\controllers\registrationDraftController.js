const RegistrationDraft = require('../models/RegistrationDraft');
const User = require('../models/User'); // Assuming User model exists
const School = require('../models/School'); // Assuming School model exists

// Create a new registration draft
exports.createRegistrationDraft = async (req, res) => {
    try {
        const {
            user_id,
            school_id,
            firstName = "",
            lastName = "",
            middleName = "",
            dateOfBirth = null,
            nationality = "",
            gender = "",
            place_of_birth = "",
            address = "",
            student_phone = "",
            student_country_code = "",
            guardian_address = "",
            guardian_phone = "",
            guardian_country_code = "",
            guardian_name = "",
            guardian_occupation = "",
            guardian_email = "",
            guardian_relationship = "",
            emergency_contact_name = "",
            emergency_contact_phone = "",
            emergency_contact_country_code = "",
            emergency_contact_relationship = "",
            previous_school = "",
            class_level = "",
            guardian_agreed_to_terms = false,
            transcript_reportcard = false,
            health_condition = "",
            doctors_name = "",
            doctors_phone = "",
            doctor_country_code = "",
            registered = false,
            selectedFees = [],
            selectedResources = [],
            paymentMode = "full",
            installments = 1,
            installmentDates = [],
            applyScholarship = false,
            scholarshipAmount = 0,
            scholarshipPercentage = 0
        } = req.body;

        // Ensure user and school exist before proceeding
        const user = await User.findById(user_id);
        const school = await School.findById(school_id);
        
        if (!user || !school) {
            return res.status(404).json({ message: 'User or School not found' });
        }

        // Prepare the draft data by spreading the values and organizing them
        const draftData = {
            user_id,
            school_id,
            firstName,
            lastName,
            middleName,
            dateOfBirth,
            nationality,
            gender,
            place_of_birth,
            address,
            student_phone,
            student_country_code,
            guardian_address,
            guardian_phone,
            guardian_country_code,
            guardian_name,
            guardian_occupation,
            guardian_email,
            guardian_relationship,
            emergency_contact_name,
            emergency_contact_phone,
            emergency_contact_country_code,
            emergency_contact_relationship,
            previous_school,
            class_level,
            guardian_agreed_to_terms,
            transcript_reportcard,
            health_condition,
            doctors_name,
            doctors_phone,
            doctor_country_code,
            registered,
            selectedFees,
            selectedResources,
            paymentMode,
            installments,
            installmentDates,
            applyScholarship,
            scholarshipAmount,
            scholarshipPercentage,
            currentStep: 1, // Default starting step
            isCompleted: false, // Default to false
            status: "draft" // Default status
        };

        // Create the new registration draft
        const newDraft = new RegistrationDraft(draftData);

        await newDraft.save();

        res.status(201).json({
            message: 'Registration draft created successfully',
            draft: newDraft,
        });
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: 'Internal Server Error' });
    }
};

// Get all registration drafts for a user and school
exports.getRegistrationDrafts = async (req, res) => {
    try {
        const { user_id, school_id } = req.params;

        // Find drafts by user and school
        const drafts = await RegistrationDraft.find({ user_id, school_id });

        if (drafts.length === 0) {
            return res.status(404).json({ message: 'No registration drafts found' });
        }

        res.status(200).json({
            message: 'Registration drafts retrieved successfully',
            drafts,
        });
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: 'Internal Server Error' });
    }
};

// Get a specific registration draft by ID
exports.getRegistrationDraftById = async (req, res) => {
    try {
        const { id } = req.params;

        const draft = await RegistrationDraft.findById(id);

        if (!draft) {
            return res.status(404).json({ message: 'Registration draft not found' });
        }

        res.status(200).json({
            message: 'Registration draft retrieved successfully',
            draft,
        });
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: 'Internal Server Error' });
    }
};

// Update a registration draft
exports.updateRegistrationDraft = async (req, res) => {
    try {
        const { draft_id } = req.params;
        const { firstName, lastName, dateOfBirth, nationality, ...otherUpdates } = req.body;

        // Check if draft exists
        const draft = await RegistrationDraft.findById(draft_id);

        if (!draft) {
            return res.status(404).json({ message: 'Registration draft not found' });
        }

        // Update the draft with the new data
        draft.firstName = firstName || draft.firstName;
        draft.lastName = lastName || draft.lastName;
        draft.dateOfBirth = dateOfBirth || draft.dateOfBirth;
        draft.nationality = nationality || draft.nationality;
        
        // Apply other updates if any
        for (let key in otherUpdates) {
            draft[key] = otherUpdates[key];
        }

        await draft.save();

        res.status(200).json({
            message: 'Registration draft updated successfully',
            draft,
        });
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: 'Internal Server Error' });
    }
};

// Delete a registration draft
// Delete a registration draft by ID
exports.deleteRegistrationDraft = async (req, res) => {
    try {
        const { draft_id } = req.params;

        // Use findByIdAndDelete to find and delete the document by its ID
        const draft = await RegistrationDraft.findByIdAndDelete(draft_id);

        if (!draft) {
            return res.status(404).json({ message: 'Registration draft not found' });
        }

        res.status(200).json({
            message: 'Registration draft deleted successfully',
        });
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: 'Internal Server Error' });
    }
};


