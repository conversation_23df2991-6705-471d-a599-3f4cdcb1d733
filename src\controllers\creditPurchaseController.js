const CreditPurchase = require('../models/CreditPurchase');
const SchoolSubscription = require('../models/SchoolSubscription');
const SubscriptionPlan = require('../models/SubscriptionPlan');
const School = require('../models/School');
const mongoose = require('mongoose');
const fapshi = require('../utils/fapshi'); // Assuming Fapshi integration exists

// Initier un achat de crédits
const initiateCreditPurchase = async (req, res) => {
  try {
    const {
      school_id,
      credits_amount,
      payment_method = 'fapshi',
      billing_info = {},
      promotion_code,
      redirect_url
    } = req.body;

    // Validation des données
    if (!school_id || !credits_amount || credits_amount < 1) {
      return res.status(400).json({ 
        message: 'School ID and valid credits amount are required' 
      });
    }

    // Vérifier que l'école existe
    const school = await School.findById(school_id);
    if (!school) {
      return res.status(404).json({ message: 'School not found' });
    }

    // Obtenir ou créer la souscription de l'école
    let subscription = await SchoolSubscription.findOne({ school_id });
    if (!subscription) {
      subscription = await SchoolSubscription.createDefaultSubscription(school_id);
    }

    // Obtenir le plan de souscription pour le prix
    const plan = await SubscriptionPlan.getPlanByName(subscription.plan_type);
    if (!plan) {
      return res.status(404).json({ message: 'Subscription plan not found' });
    }

    // Vérifier le minimum d'achat
    if (credits_amount < plan.minimum_purchase) {
      return res.status(400).json({ 
        message: `Minimum purchase is ${plan.minimum_purchase} credits` 
      });
    }

    // Calculer le prix
    const price_per_credit = plan.price_per_credit;
    let total_amount = credits_amount * price_per_credit;
    let discount_amount = 0;
    let discount_percentage = 0;

    // Appliquer la promotion si fournie
    if (promotion_code) {
      // TODO: Implémenter la logique de promotion
      // Pour l'instant, on ignore les codes promo
    }

    total_amount -= discount_amount;

    // Générer les IDs de transaction
    const purchase_id = CreditPurchase.generatePurchaseId();

    // Préparer les données pour le CreditPurchase (sans le créer encore)
    const creditPurchaseData = {
      school_id,
      subscription_id: subscription._id,
      purchase_id,
      credits_purchased: credits_amount,
      price_per_credit,
      total_amount,
      currency: 'XAF',
      payment_method,
      payment_status: 'pending',
      purchased_by: req.user.id,
      purchaser_email: billing_info?.email || req.user.email,
      billing_info,
      promotion_code,
      discount_amount,
      discount_percentage,
      purchase_date: new Date()
    };

    // Initier le paiement selon la méthode choisie
    let payment_response = {};
    let creditPurchase = null;

    if (payment_method === 'fapshi') {
      try {
        // Utiliser les données de billing_info du modal en priorité, sinon celles de req.user
        const paymentEmail = billing_info?.email || req.user.email;
        const paymentName = billing_info?.name || req.user.name || req.user.username;
        const paymentPhone = billing_info?.phone || req.user.phone;

        console.log(`� Initiation paiement Fapshi pour ${credits_amount} crédits (${total_amount} XAF)`);
        console.log(`�📧 Données de paiement utilisées:`, {
          email: paymentEmail,
          name: paymentName,
          phone: paymentPhone,
          source: billing_info?.email ? 'modal' : 'user_profile'
        });

        // D'abord, appeler Fapshi pour initier le paiement
        payment_response = await fapshi.initiatePay({
          userId: req.user.id,
          amount: total_amount,
          email: paymentEmail,
          name: paymentName,  
          phone: paymentPhone,
          externalId: purchase_id,
          redirectUrl: redirect_url || `${process.env.FRONTEND_URL}/school-admin/buy-credit/success`,
          message: `Achat de ${credits_amount} crédits${paymentName ? ` par ${paymentName}` : ''}`
        });

        // Vérifier si Fapshi a réussi avant de créer le CreditPurchase
        if (payment_response.statusCode !== 200) {
          console.error('❌ Fapshi initiation failed:', payment_response);
          return res.status(payment_response.statusCode || 400).json({
            message: 'Payment initiation failed',
            error: payment_response.message || 'Unknown error from payment provider',
            details: payment_response
          });
        }

        // Maintenant créer le CreditPurchase avec le transId de Fapshi
        creditPurchase = new CreditPurchase({
          ...creditPurchaseData,
          transaction_id: payment_response.transId || `TXN_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          payment_gateway_response: payment_response,
          metadata: {
            fapshi_initiated_at: new Date(),
            fapshi_link: payment_response.link,
            payment_provider: 'fapshi',
            purchase_id: purchase_id
          }
        });

        await creditPurchase.save();

        console.log(`✅ CreditPurchase créé après succès Fapshi:`, {
          transId: payment_response.transId,
          link: payment_response.link,
          statusCode: payment_response.statusCode,
          purchase_id: creditPurchase.purchase_id,
          transaction_id: creditPurchase.transaction_id
        });

      } catch (paymentError) {
        console.error('❌ Payment initiation error:', paymentError);

        return res.status(500).json({
          message: 'Payment initiation failed',
          error: paymentError.message
        });
      }
    } else {
      // Pour les autres méthodes de paiement (si elles existent)
      creditPurchase = new CreditPurchase(creditPurchaseData);
      await creditPurchase.save();
    }

    res.status(201).json({
      purchase: {
        purchase_id: creditPurchase.purchase_id,
        transaction_id: creditPurchase.transaction_id,
        credits_purchased: creditPurchase.credits_purchased,
        total_amount: creditPurchase.total_amount,
        payment_status: creditPurchase.payment_status
      },
      payment_response,
      message: 'Credit purchase initiated successfully'
    });
  } catch (error) {
    console.error('Error initiating credit purchase:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Confirmer un achat de crédits (webhook ou vérification manuelle)
const confirmCreditPurchase = async (req, res) => {
  try {
    const { transaction_id, payment_status, gateway_response = {} } = req.body;

    if (!transaction_id) {
      return res.status(400).json({ message: 'Transaction ID is required' });
    }

    const creditPurchase = await CreditPurchase.findOne({ transaction_id })
      .populate('subscription_id');

    if (!creditPurchase) {
      return res.status(404).json({ message: 'Credit purchase not found' });
    }

    if (creditPurchase.payment_status === 'completed') {
      return res.status(400).json({ message: 'Purchase already completed' });
    }

    if (payment_status === 'completed' || payment_status === 'SUCCESSFUL') {
      await creditPurchase.markAsCompleted(gateway_response);
      
      res.status(200).json({
        purchase: creditPurchase,
        message: 'Credit purchase confirmed successfully'
      });
    } else {
      await creditPurchase.markAsFailed(`Payment failed: ${payment_status}`);
      
      res.status(400).json({
        purchase: creditPurchase,
        message: 'Credit purchase failed'
      });
    }
  } catch (error) {
    console.error('Error confirming credit purchase:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Obtenir l'historique des achats d'une école
const getPurchaseHistory = async (req, res) => {
  try {
    const { school_id } = req.params;
    const { limit = 20, skip = 0 } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    const purchases = await CreditPurchase.find({ school_id })
      .populate('purchased_by', 'name email')
      .sort({ purchase_date: -1 })
      .limit(parseInt(limit))
      .skip(parseInt(skip));

    const totalCount = await CreditPurchase.countDocuments({ school_id });

    res.status(200).json({
      purchases,
      pagination: {
        total: totalCount,
        limit: parseInt(limit),
        skip: parseInt(skip),
        has_more: (parseInt(skip) + parseInt(limit)) < totalCount
      },
      message: 'Purchase history retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching purchase history:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Obtenir les détails d'un achat spécifique
const getPurchaseDetails = async (req, res) => {
  try {
    const { purchase_id } = req.params;

    if (!purchase_id) {
      return res.status(400).json({ message: 'Purchase ID is required' });
    }

    const purchase = await CreditPurchase.findOne({ purchase_id })
      .populate('school_id', 'name email')
      .populate('subscription_id')
      .populate('purchased_by', 'name email')
      .populate('processed_by', 'name email');

    if (!purchase) {
      return res.status(404).json({ message: 'Purchase not found' });
    }

    res.status(200).json({
      purchase,
      message: 'Purchase details retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching purchase details:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Vérifier le statut d'un paiement
const checkPaymentStatus = async (req, res) => {
  try {
    const { transaction_id } = req.params;

    if (!transaction_id) {
      return res.status(400).json({ message: 'Transaction ID is required' });
    }

    // Rechercher par transaction_id (qui est maintenant le transId de Fapshi)
    const purchase = await CreditPurchase.findOne({ transaction_id });

    if (!purchase) {
      console.log(`❌ Transaction non trouvée pour ID: ${transaction_id}`);
      return res.status(404).json({ message: 'Transaction not found' });
    }

    console.log(`✅ Transaction trouvée:`, {
      purchase_id: purchase.purchase_id,
      transaction_id: purchase.transaction_id,
      payment_status: purchase.payment_status
    });
    let fapshiStatus ;
    // Vérifier le statut auprès de Fapshi si le paiement est en attente
    if (purchase.payment_status === 'pending' && purchase.payment_method === 'fapshi') {
      try {
        // Notre transaction_id est maintenant directement le transId de Fapshi
        console.log(`🔍 Vérification du statut Fapshi pour transId: ${transaction_id}`);
        fapshiStatus = await fapshi.paymentStatus(transaction_id);

        console.log(`📦 Statut Fapshi reçu:`, fapshiStatus);

        if (fapshiStatus.status === 'SUCCESSFUL' && purchase.payment_status !== 'completed') {
          console.log(`✅ Paiement confirmé comme réussi par Fapshi`);
          await purchase.markAsCompleted(fapshiStatus);
        }else if (fapshiStatus.status === 'SUCCESSFUL' && purchase.payment_status === 'pending') {
          console.log(`✅ Paiement confirmé comme réussi par Fapshi`);
          await purchase.markAsCompleted(fapshiStatus);
        } else if (fapshiStatus.status === 'FAILED' && purchase.payment_status === 'pending') {
          console.log(`❌ Paiement confirmé comme échoué par Fapshi`);
          await purchase.markAsFailed('Payment failed at gateway');
        }else if (fapshiStatus.status == 'CREATED'){
          console.log(`❌ Paiement confirmé comme échoué par Fapshi`);
          // await purchase.markAsFailed('Payment failed at gateway');
        } else {
          console.log(`⏳ Statut Fapshi: ${fapshiStatus.status}`);
        }
      } catch (fapshiError) {
        console.error('Error checking Fapshi status:', fapshiError);
      }
    }

    res.status(200).json({
      transaction_id: purchase.transaction_id,
      purchase_id: purchase.purchase_id,
      payment_status: purchase.payment_status,
      credits_purchased: purchase.credits_purchased,
      total_amount: purchase.total_amount,
      purchase_date: purchase.purchase_date,
      payment_completed_date: purchase.payment_completed_date,
      fapshiStatus,
      message: 'Payment status retrieved successfully'
    });
  } catch (error) {
    console.error('Error checking payment status:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Effectuer un payout (remboursement) pour une transaction problématique
const processRefund = async (req, res) => {
  try {
    const {
      transaction_id,
      phone,
      reason,
      amount, // Optionnel, si différent du montant original
      medium // Optionnel: "mobile money" ou "orange money"
    } = req.body;

    // Validation des données requises
    if (!transaction_id || !phone || !reason) {
      return res.status(400).json({
        message: 'Transaction ID, phone number, and reason are required',
        error_code: 'VALIDATION_ERROR',
        error_type: 'missing_required_fields',
        details: {
          missing_fields: [
            !transaction_id && 'transaction_id',
            !phone && 'phone',
            !reason && 'reason'
          ].filter(Boolean)
        }
      });
    }

    // Validation du format du numéro de téléphone
    const phoneRegex = /^6[0-9]{8}$/;
    if (!phoneRegex.test(phone)) {
      return res.status(400).json({
        message: 'Phone number must be in format 6XXXXXXXX',
        error_code: 'VALIDATION_ERROR',
        error_type: 'invalid_phone_format',
        details: {
          provided_phone: phone,
          expected_format: '6XXXXXXXX'
        }
      });
    }

    // Rechercher la transaction originale
    const originalPurchase = await CreditPurchase.findOne({
      transaction_id: transaction_id
    }).populate('school_id');

    if (!originalPurchase) {
      return res.status(404).json({
        message: 'Original transaction not found',
        error_code: 'TRANSACTION_NOT_FOUND',
        error_type: 'resource_not_found',
        details: {
          transaction_id: transaction_id,
          searched_field: 'transaction_id'
        }
      });
    }

    // Vérifier que la transaction peut être remboursée
    if (originalPurchase.payment_status === 'refunded') {
      return res.status(400).json({
        message: 'Transaction has already been refunded',
        error_code: 'ALREADY_REFUNDED',
        error_type: 'business_logic_error',
        details: {
          transaction_id: transaction_id,
          current_status: originalPurchase.payment_status,
          refund_info: originalPurchase.metadata?.refund_date ? {
            refund_date: originalPurchase.metadata.refund_date,
            refund_transaction_id: originalPurchase.metadata.refund_transaction_id
          } : null
        }
      });
    }

    // Vérifier que la transaction était bien complétée avant de permettre le remboursement
    // Seules les transactions complétées peuvent être remboursées car elles ont ajouté des crédits
    if (originalPurchase.payment_status !== 'completed') {
      return res.status(400).json({
        message: `Cannot refund transaction with status '${originalPurchase.payment_status}'. Only completed transactions can be refunded.`,
        error_code: 'INVALID_TRANSACTION_STATUS',
        error_type: 'business_logic_error',
        details: {
          transaction_id: transaction_id,
          current_status: originalPurchase.payment_status,
          required_status: 'completed',
          allowed_statuses: ['completed']
        }
      });
    }

    // Utiliser le montant original si aucun montant spécifique n'est fourni
    const refundAmount = amount || originalPurchase.total_amount;

    // Vérifier que le montant de remboursement ne dépasse pas le montant original
    if (refundAmount > originalPurchase.total_amount) {
      return res.status(400).json({
        message: 'Refund amount cannot exceed original transaction amount',
        error_code: 'INVALID_REFUND_AMOUNT',
        error_type: 'business_logic_error',
        details: {
          requested_amount: refundAmount,
          original_amount: originalPurchase.total_amount,
          maximum_allowed: originalPurchase.total_amount
        }
      });
    }

    // Vérifier que le montant est valide (minimum 100 XAF)
    if (refundAmount < 100) {
      return res.status(400).json({
        message: 'Refund amount cannot be less than 100 XAF',
        error_code: 'INVALID_REFUND_AMOUNT',
        error_type: 'business_logic_error',
        details: {
          requested_amount: refundAmount,
          minimum_amount: 100
        }
      });
    }

    console.log(`🔄 Processing refund for transaction ${transaction_id}:`, {
      originalAmount: originalPurchase.total_amount,
      refundAmount,
      phone,
      reason
    });

    // Préparer les données pour le payout Fapshi
    const payoutData = {
      amount: refundAmount,
      phone: phone,
      medium: medium,
      name: originalPurchase.school_id?.name || 'Client',
      email: originalPurchase.billing_info?.email,
      userId: originalPurchase.school_id?._id?.toString(),
      externalId: `REFUND_${transaction_id}_${Date.now()}`,
      message: `Remboursement: ${reason} (Transaction: ${transaction_id})`
    };

    // Effectuer le payout via Fapshi
    const payoutResponse = await fapshi.payout(payoutData);

    if (payoutResponse.statusCode !== 200) {
      console.error('❌ Fapshi payout failed:', payoutResponse);

      // Analyser le type d'erreur Fapshi pour fournir des détails plus précis
      let errorType = 'payment_provider_error';
      let errorCode = 'FAPSHI_PAYOUT_FAILED';

      if (payoutResponse.statusCode === 400) {
        errorType = 'validation_error';
        errorCode = 'FAPSHI_VALIDATION_ERROR';
      } else if (payoutResponse.statusCode === 401) {
        errorType = 'authentication_error';
        errorCode = 'FAPSHI_AUTH_ERROR';
      } else if (payoutResponse.statusCode === 403) {
        errorType = 'authorization_error';
        errorCode = 'FAPSHI_PERMISSION_ERROR';
      } else if (payoutResponse.statusCode >= 500) {
        errorType = 'service_unavailable';
        errorCode = 'FAPSHI_SERVER_ERROR';
      }

      return res.status(payoutResponse.statusCode || 400).json({
        message: 'Refund failed due to payment provider error',
        error_code: errorCode,
        error_type: errorType,
        provider_error: payoutResponse.message || 'Unknown error from payment provider',
        details: {
          transaction_id: transaction_id,
          refund_amount: refundAmount,
          phone: phone,
          provider_status_code: payoutResponse.statusCode,
          provider_response: payoutResponse,
          payout_data: {
            amount: payoutData.amount,
            phone: payoutData.phone,
            medium: payoutData.medium,
            externalId: payoutData.externalId
          }
        }
      });
    }

    // Créer un enregistrement de remboursement
    const refundRecord = new CreditPurchase({
      school_id: originalPurchase.school_id._id,
      transaction_id: payoutResponse.transId || `REFUND_${Date.now()}`,
      purchase_id: `REFUND_${originalPurchase.purchase_id}`,
      credits_purchased: -Math.abs(originalPurchase.credits_purchased), // Négatif pour indiquer un remboursement
      total_amount: -Math.abs(refundAmount), // Négatif pour indiquer un remboursement
      payment_method: 'fapshi_payout',
      payment_status: 'refund_completed',
      billing_info: {
        ...originalPurchase.billing_info,
        refund_phone: phone,
        refund_reason: reason,
        original_transaction_id: transaction_id
      },
      purchase_date: new Date(),
      payment_completed_date: new Date(),
      metadata: {
        type: 'refund',
        original_transaction_id: transaction_id,
        refund_reason: reason,
        payout_response: payoutResponse
      }
    });

    await refundRecord.save();

    // Mettre à jour la transaction originale
    originalPurchase.payment_status = 'refunded';
    originalPurchase.metadata = {
      ...originalPurchase.metadata,
      refunded: true,
      refund_transaction_id: refundRecord.transaction_id,
      refund_date: new Date(),
      refund_reason: reason
    };
    await originalPurchase.save();

    // Mettre à jour les crédits de l'école (retirer les crédits remboursés)
    // Note: On arrive ici seulement si originalPurchase.payment_status === 'completed'
    // grâce à la vérification ajoutée plus haut
    const subscription = await SchoolSubscription.findOne({
      school_id: originalPurchase.school_id._id
    });

    if (subscription) {
      // Vérifier que l'école a suffisamment de crédits avant de les retirer
      if (subscription.available_credits >= originalPurchase.credits_purchased) {
        subscription.available_credits -= originalPurchase.credits_purchased;
        // Aussi ajuster le total des crédits achetés
        subscription.total_credits_purchased = Math.max(0,
          subscription.total_credits_purchased - originalPurchase.credits_purchased
        );
        // Ajuster le montant total payé
        subscription.total_amount_paid = Math.max(0,
          subscription.total_amount_paid - refundAmount
        );
        await subscription.save();
        console.log(`📉 Removed ${originalPurchase.credits_purchased} credits from school ${originalPurchase.school_id.name}`);
      } else {
        console.warn(`⚠️ School ${originalPurchase.school_id.name} has insufficient credits (${subscription.available_credits}) to remove ${originalPurchase.credits_purchased} credits`);
        // On continue quand même le remboursement mais on log l'incohérence
      }
    }

    console.log('✅ Refund processed successfully:', {
      refundTransactionId: refundRecord.transaction_id,
      payoutTransId: payoutResponse.transId,
      amount: refundAmount
    });

    res.status(200).json({
      message: 'Refund processed successfully',
      success: true,
      refund_transaction_id: refundRecord.transaction_id,
      payout_transaction_id: payoutResponse.transId,
      refund_amount: refundAmount,
      original_transaction_id: transaction_id,
      payout_details: {
        phone: phone,
        medium: payoutResponse.medium || medium,
        dateInitiated: payoutResponse.dateInitiated
      },
      processing_details: {
        credits_removed: originalPurchase.credits_purchased,
        school_name: originalPurchase.school_id.name,
        refund_reason: reason,
        processed_at: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ Error processing refund:', error);

    // Analyser le type d'erreur pour fournir des détails plus précis
    let errorType = 'internal_server_error';
    let errorCode = 'REFUND_PROCESSING_ERROR';

    if (error.name === 'ValidationError') {
      errorType = 'validation_error';
      errorCode = 'DATABASE_VALIDATION_ERROR';
    } else if (error.name === 'MongoError' || error.name === 'MongoServerError') {
      errorType = 'database_error';
      errorCode = 'DATABASE_ERROR';
    } else if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      errorType = 'network_error';
      errorCode = 'NETWORK_CONNECTION_ERROR';
    }

    res.status(500).json({
      message: 'Internal server error while processing refund',
      error_code: errorCode,
      error_type: errorType,
      error_message: error.message,
      details: {
        transaction_id: req.body.transaction_id,
        error_name: error.name,
        error_stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      }
    });
  }
};

// Récupérer les transactions problématiques nécessitant une attention
const getProblematicTransactions = async (req, res) => {
  console.log(" 🔍 in the  /api/credit-purchase/problematic endpoint...")
  try {
    const currentTime = new Date();
    const oneDayAgo = new Date(currentTime.getTime() - 24 * 60 * 60 * 1000);
    const twoDaysAgo = new Date(currentTime.getTime() - 48 * 60 * 60 * 1000);

    // Rechercher les transactions problématiques
    const problematicTransactions = await CreditPurchase.find({
      $or: [
        // Transactions en attente depuis plus de 1 jour
        {
          payment_status: 'pending',
          purchase_date: { $lt: oneDayAgo }
        },
        // Transactions échouées
        {
          payment_status: 'failed'
        },
        // Transactions expirées
        {
          payment_status: 'expired'
        }
      ]
    })
    .populate('school_id', 'name email phone')
    .sort({ purchase_date: -1 })
    .limit(100);

    // Enrichir les données avec des informations supplémentaires
    const enrichedTransactions = await Promise.all(
      problematicTransactions.map(async (transaction) => {
        const transactionObj = transaction.toObject();

        // Calculer le nombre de jours en attente
        const daysPending = Math.floor(
          (currentTime.getTime() - new Date(transaction.purchase_date).getTime()) / (24 * 60 * 60 * 1000)
        );

        // Vérifier le statut Fapshi si applicable
        let fapshiStatus = null;
        if (transaction.payment_method === 'fapshi' && transaction.transaction_id) {
          try {
            const fapshiResponse = await fapshi.paymentStatus(transaction.transaction_id);
            fapshiStatus = fapshiResponse.status;

            // Mettre à jour le statut local si nécessaire
            if (fapshiResponse.status === 'SUCCESSFUL' && transaction.payment_status === 'pending') {
              transaction.payment_status = 'completed';
              transaction.payment_completed_date = new Date();
              await transaction.save();
              return null; // Exclure cette transaction car elle est maintenant complétée
            }
          } catch (error) {
            console.error(`Error checking Fapshi status for ${transaction.transaction_id}:`, error);
          }
        }

        return {
          ...transactionObj,
          days_pending: daysPending,
          fapshi_status: fapshiStatus
        };
      })
    );

    // Filtrer les transactions nulles (celles qui ont été mises à jour comme complétées)
    const validTransactions = enrichedTransactions.filter(t => t !== null);

    console.log(`📊 Found ${validTransactions.length} problematic transactions`);

    res.status(200).json(validTransactions);
  } catch (error) {
    console.error('Error fetching problematic transactions:', error);
    res.status(500).json({
      message: 'Internal server error while fetching problematic transactions',
      error: error.message
    });
  }
};

// Obtenir les statistiques du service de surveillance
const getMonitoringStats = async (req, res) => {
  try {
    const paymentMonitoringService = require('../services/paymentMonitoringService');
    const stats = await paymentMonitoringService.getMonitoringStats();

    if (!stats) {
      return res.status(500).json({
        message: 'Unable to retrieve monitoring statistics'
      });
    }

    res.status(200).json(stats);
  } catch (error) {
    console.error('Error getting monitoring stats:', error);
    res.status(500).json({
      message: 'Internal server error while getting monitoring stats',
      error: error.message
    });
  }
};

module.exports = {
  initiateCreditPurchase,
  confirmCreditPurchase,
  getPurchaseHistory,
  getPurchaseDetails,
  checkPaymentStatus,
  processRefund,
  getProblematicTransactions,
  getMonitoringStats
};
