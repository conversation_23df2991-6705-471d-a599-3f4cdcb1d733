// Base schema (raw MongoDB document)
export interface AttendanceJustificationSchema extends Record<string, unknown> {
  _id: string;
  attendance_id: string; // ObjectId string reference
  submitted_by: string; // ObjectId string
  justification_type: "Text" | "File" | "TextAndFile";
  text?: string | null;
  file_url?: string | null;
  status: "Pending" | "Accepted" | "Rejected";
  reviewed_by?: string | null;
  review_comment?: string | null;
  createdAt: string;
  updatedAt: string;
}

// Populated version (used in GET routes)
export interface PopulatedAttendanceJustification extends Record<string, unknown> {
  _id: string;
  attendance_id: {
    _id: string;
    date: string;
    academic_year: string;
    status: string;
    student_id: {
      _id: string;
      student_id: string;
      first_name: string;
      last_name: string;
      school_id: string;
    };
    school_id: {
      _id: string;
      name: string;
    };
    schedule_id?: string | null;
  };
  submitted_by: {
    _id: string;
    name?: string | null;
    email?: string | null;
    role: string;
  };
  justification_type: "Text" | "File" | "TextAndFile";
  text?: string | null;
  file_url?: string | null;
  status: "Pending" | "Accepted" | "Rejected";
  reviewed_by?: {
    _id: string;
    name?: string | null;
    email?: string | null;
    role: string;
  } | null;
  review_comment?: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface AttendanceJustificationDeleteSchema {
  _id: string;
}

export interface AttendanceJustificationUpdateSchema {
  _id: string;
  text?: string | null;
  file_url?: string | null;
  status?: "Pending" | "Accepted" | "Rejected";
  review_comment?: string | null;
  reviewed_by?: string | null;
}

export type SemiPopulatedJustification = Omit<PopulatedAttendanceJustification, 'attendance_id' | 'submitted_by' | 'reviewed_by'> & {
  attendance_id: string | PopulatedAttendanceJustification['attendance_id'];
  submitted_by: string | PopulatedAttendanceJustification['submitted_by'];
  reviewed_by?: string | PopulatedAttendanceJustification['reviewed_by'];
};
