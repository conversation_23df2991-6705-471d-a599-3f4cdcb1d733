# 🔄 Guide du Système de Remboursement Intelligent

## 📋 Vue d'ensemble

Ce système détecte automatiquement les transactions Fapshi problématiques et permet aux administrateurs d'effectuer des remboursements rapides et sécurisés.

## 🚀 Activation du Système

### 1. Vérification des Prérequis

```bash
# Vérifier que node-cron est installé
npm list node-cron

# Si pas installé
npm install node-cron
```

### 2. Démarrage du Serveur

```bash
# Démarrer le serveur backend
npm run dev
```

Le service de surveillance se lance automatiquement et affiche :
```
🔍 Payment monitoring service started
```

### 3. Test du Système

```bash
# Exécuter les tests de validation
node test-refund-system.js
```

## 🎯 Fonctionnalités Principales

### 🔍 Surveillance Automatique

- **Fréquence** : Toutes les 30 minutes
- **Détection** : Transactions en attente > 1h, échouées, expirées
- **Action** : Vérification automatique du statut Fapshi
- **Mise à jour** : Statuts automatiquement synchronisés

### 🏪 Interface d'Administration

**Accès** : `/super-admin/refunds`

**Fonctionnalités** :
- Vue d'ensemble des transactions problématiques
- Filtres par statut et recherche
- Statistiques en temps réel
- Actions de remboursement en un clic

### 💰 Processus de Remboursement

1. **Identification** : Transaction problématique détectée
2. **Vérification** : Statut `completed` requis pour remboursement
3. **Validation** : Crédits suffisants dans le compte école
4. **Exécution** : Payout via API Fapshi
5. **Traçabilité** : Enregistrement complet de l'opération

## 🛠️ Utilisation Pratique

### Accéder à l'Interface de Remboursement

1. Connectez-vous en tant que Super Admin
2. Naviguez vers **Remboursements** dans le menu
3. Consultez la liste des transactions problématiques

### Effectuer un Remboursement

1. **Sélectionner** une transaction dans la liste
2. **Cliquer** sur "Rembourser"
3. **Remplir** le formulaire :
   - Numéro de téléphone (format : 6XXXXXXXX)
   - Type de compte (Mobile Money / Orange Money)
   - Montant (par défaut : montant original)
   - Raison du remboursement
4. **Confirmer** l'opération

### Vérifier le Statut

- **Notifications** : Alertes automatiques pour les problèmes
- **Logs** : Console du serveur pour le suivi détaillé
- **Historique** : Enregistrements dans la base de données

## 📊 API Endpoints

### Transactions Problématiques
```
GET /api/credit-purchase/problematic
Authorization: Bearer <admin_token>
```

### Effectuer un Remboursement
```
POST /api/credit-purchase/refund
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "transaction_id": "TXN_123456789",
  "phone": "677123456",
  "reason": "Paiement non traité par Fapshi",
  "amount": 5000,
  "medium": "mobile money"
}
```

### Statistiques de Surveillance
```
GET /api/credit-purchase/monitoring/stats
Authorization: Bearer <admin_token>
```

## 🔒 Sécurité et Validations

### Contrôles d'Accès
- **Authentification** : Token Firebase requis
- **Autorisation** : Rôles `admin` ou `super` uniquement
- **Validation** : Vérification des données avant traitement

### Validations Métier
- **Statut Transaction** : Seules les transactions `completed` peuvent être remboursées
- **Crédits École** : Vérification des crédits disponibles avant retrait
- **Format Téléphone** : Validation du format camerounais (6XXXXXXXX)
- **Montant** : Ne peut pas dépasser le montant original

### Traçabilité
- **Logs Détaillés** : Chaque action est enregistrée
- **Métadonnées** : Informations complètes sur les remboursements
- **Audit Trail** : Historique complet des opérations

## 🚨 Gestion des Erreurs

### Erreurs Communes

1. **"Transaction not found"**
   - Vérifier l'ID de transaction
   - S'assurer que la transaction existe

2. **"Cannot refund non-completed transaction"**
   - Seules les transactions complétées peuvent être remboursées
   - Vérifier le statut de la transaction

3. **"Insufficient credits"**
   - L'école n'a pas assez de crédits
   - Vérifier le solde de crédits de l'école

4. **"Invalid phone format"**
   - Utiliser le format 6XXXXXXXX
   - Numéro camerounais uniquement

### Actions de Dépannage

1. **Vérifier les Logs**
   ```bash
   # Surveiller les logs du serveur
   tail -f logs/server.log
   ```

2. **Redémarrer la Surveillance**
   ```javascript
   // Dans la console Node.js
   const paymentMonitoringService = require('./src/services/paymentMonitoringService');
   paymentMonitoringService.stop();
   paymentMonitoringService.start();
   ```

3. **Vérifier la Configuration Fapshi**
   ```bash
   # Vérifier les variables d'environnement
   echo $FAPSHI_API_USER
   echo $FAPSHI_API_KEY
   echo $FAPSHI_BASE_URL
   ```

## 📈 Monitoring et Maintenance

### Surveillance Quotidienne
- Vérifier les notifications de transactions problématiques
- Consulter les statistiques de surveillance
- Examiner les logs pour les erreurs

### Maintenance Hebdomadaire
- Analyser les tendances de remboursement
- Vérifier la cohérence des données de crédits
- Optimiser les seuils de détection si nécessaire

### Rapports Mensuels
- Statistiques des remboursements effectués
- Analyse des causes principales de problèmes
- Recommandations d'amélioration

## 🆘 Support et Contact

En cas de problème :

1. **Consulter les logs** du serveur
2. **Vérifier la documentation** Fapshi
3. **Tester avec le script** `test-refund-system.js`
4. **Contacter l'équipe technique** avec les détails de l'erreur

## 📝 Notes Importantes

- ⚠️ **Toujours tester** en environnement de développement avant la production
- 🔄 **Sauvegarder** la base de données avant les opérations de masse
- 📊 **Surveiller** régulièrement les métriques de performance
- 🔐 **Maintenir** les credentials Fapshi à jour

---

**Version** : 1.0  
**Dernière mise à jour** : Décembre 2024  
**Compatibilité** : Node.js 16+, MongoDB 4.4+
