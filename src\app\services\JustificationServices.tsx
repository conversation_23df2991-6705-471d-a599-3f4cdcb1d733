import { BASE_API_URL } from "./AuthContext";
import { getTokenFromCookie } from "./UserServices";
import {
  AttendanceJustificationSchema,
  PopulatedAttendanceJustification,
  AttendanceJustificationUpdateSchema,
} from "../models/Justification";

// Helper to get auth headers
function getAuthHeaders() {
  const token = getTokenFromCookie("idToken");
  if (!token) throw new Error("No authentication token found");
  return {
    "Content-Type": "application/json",
    Authorization: `Bearer ${token}`,
  };
}

// Create a new justification (with optional file upload)
export async function createJustification(
  attendance_id: string,
  text?: string,
  file?: File
): Promise<AttendanceJustificationSchema | null> {
  try {
    const token = getTokenFromCookie("idToken");
    if (!token) throw new Error("No authentication token found");

    const formData = new FormData();
    formData.append("attendance_id", attendance_id);
    if (text) formData.append("text", text);
    if (file) formData.append("file", file);

    const response = await fetch(`${BASE_API_URL}/justification/`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    });

    if (!response.ok) {
      throw new Error(
        `Failed to create justification: ${response.status} ${response.statusText}`
      );
    }

    const data = await response.json();
    return data.justification as AttendanceJustificationSchema;
  } catch (error) {
    console.error("Create justification error:", error);
    return null;
  }
}

// Get justification by ID (populated)
export async function getJustificationById(
  id: string
): Promise<PopulatedAttendanceJustification | null> {
  try {
    const response = await fetch(`${BASE_API_URL}/justification/${id}`, {
      method: "GET",
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      if (response.status === 404) return null;
      throw new Error(
        `Failed to fetch justification: ${response.status} ${response.statusText}`
      );
    }

    const data = await response.json();
    return data as PopulatedAttendanceJustification;
  } catch (error) {
    console.error("Get justification by ID error:", error);
    return null;
  }
}

// Get justifications by school_id
export async function getJustificationsBySchool(
  school_id: string
): Promise<PopulatedAttendanceJustification[]> {
  try {
    const response = await fetch(
      `${BASE_API_URL}/justification/school/${school_id}`,
      {
        method: "GET",
        headers: getAuthHeaders(),
      }
    );

    if (!response.ok) {
      throw new Error(
        `Failed to fetch justifications by school: ${response.status} ${response.statusText}`
      );
    }

    const data = await response.json();
    return data as PopulatedAttendanceJustification[];
  } catch (error) {
    console.error("Get justifications by school error:", error);
    return [];
  }
}

// Get justifications by student_id
export async function getJustificationsByStudent(
  student_id: string
): Promise<PopulatedAttendanceJustification[]> {
  try {
    const response = await fetch(
      `${BASE_API_URL}/justification/student/${student_id}`,
      {
        method: "GET",
        headers: getAuthHeaders(),
      }
    );

    if (!response.ok) {
      throw new Error(
        `Failed to fetch justifications by student: ${response.status} ${response.statusText}`
      );
    }

    const data = await response.json();
    return data as PopulatedAttendanceJustification[];
  } catch (error) {
    console.error("Get justifications by student error:", error);
    return [];
  }
}

// Review a justification (update status, comment, reviewer)
export async function reviewJustification(
  id: string,
  status: "Pending" | "Accepted" | "Rejected",
  review_comment?: string
): Promise<AttendanceJustificationSchema | null> {
  try {
    const response = await fetch(`${BASE_API_URL}/justification/${id}/review`, {
      method: "PATCH",
      headers: getAuthHeaders(),
      body: JSON.stringify({ status, review_comment }),
    });

    if (!response.ok) {
      throw new Error(
        `Failed to review justification: ${response.status} ${response.statusText}`
      );
    }

    const data = await response.json();
    return data.justification as AttendanceJustificationSchema;
  } catch (error) {
    console.error("Review justification error:", error);
    return null;
  }
}

// Update justification general fields (text, file_url, status, etc.)
export async function updateJustification(
  updateData: AttendanceJustificationUpdateSchema
): Promise<AttendanceJustificationSchema | null> {
  try {
    const { _id, ...fieldsToUpdate } = updateData;

    const response = await fetch(`${BASE_API_URL}/justification/${_id}`, {
      method: "PATCH",
      headers: getAuthHeaders(),
      body: JSON.stringify(fieldsToUpdate),
    });

    if (!response.ok) {
      throw new Error(
        `Failed to update justification: ${response.status} ${response.statusText}`
      );
    }

    const data = await response.json();
    return data.justification as AttendanceJustificationSchema;
  } catch (error) {
    console.error("Update justification error:", error);
    return null;
  }
}

// Delete a justification by ID
export async function deleteJustification(
  id: string
): Promise<boolean> {
  try {
    const response = await fetch(`${BASE_API_URL}/justification/${id}`, {
      method: "DELETE",
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error(
        `Failed to delete justification: ${response.status} ${response.statusText}`
      );
    }

    return true;
  } catch (error) {
    console.error("Delete justification error:", error);
    return false;
  }
}
