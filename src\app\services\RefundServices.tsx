import { BASE_API_URL } from "./AuthContext";
import { getTokenFromCookie } from "./UserServices";
// const BASE_API_URL = 'http://localhost:3002/api'
export interface ProblematicTransaction {
  _id: string;
  transaction_id: string;
  purchase_id: string;
  school_id: {
    _id: string;
    name: string;
    email?: string;
    phone?: string;
  };
  credits_purchased: number;
  total_amount: number;
  payment_status: 'pending' | 'failed' | 'expired';
  payment_method: string;
  billing_info: {
    name?: string;
    email?: string;
    phone?: string;
  };
  purchase_date: string;
  payment_completed_date?: string;
  metadata?: any;
  fapshi_status?: string;
  days_pending?: number;
}

export interface RefundRequest {
  transaction_id: string;
  phone: string;
  reason: string;
  amount?: number;
  medium?: 'mobile money' | 'orange money';
}

export interface RefundResponse {
  message: string;
  refund_transaction_id: string;
  payout_transaction_id: string;
  refund_amount: number;
  original_transaction_id: string;
  payout_details: {
    phone: string;
    medium?: string;
    dateInitiated: string;
  };
}

export interface MonitoringStats {
  period: string;
  stats: Array<{
    _id: string;
    count: number;
    totalAmount: number;
  }>;
  isMonitoring: boolean;
}

/**
 * Récupère les transactions problématiques
 */
export async function getProblematicTransactions(): Promise<ProblematicTransaction[]> {
  const token = getTokenFromCookie("idToken");

  if (!token) {
    throw new Error("No authentication token found");
  }

  try {
    const response = await fetch(`${BASE_API_URL}/credit-purchase/problematic`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));

      // Si c'est une erreur 404 ou "Purchase not found", retourner un tableau vide
      if (response.status === 404 || errorData.message?.includes("Purchase not found")) {
        console.log("No problematic transactions found, returning empty array");
        return [];
      }

      throw new Error(errorData.message || `Failed to fetch problematic transactions: ${response.status}`);
    }

    const data = await response.json();
    return Array.isArray(data) ? data : [];
  } catch (error: any) {
    // Si c'est une erreur réseau ou de parsing, retourner un tableau vide
    if (error.name === 'TypeError' || error.message?.includes('Failed to fetch')) {
      console.warn("Network error fetching problematic transactions, returning empty array");
      return [];
    }
    throw error;
  }
}

/**
 * Effectue un remboursement pour une transaction
 */
export async function processRefund(refundData: RefundRequest): Promise<RefundResponse> {
  const token = getTokenFromCookie("idToken");
  
  if (!token) {
    throw new Error("No authentication token found");
  }

  const response = await fetch(`${BASE_API_URL}/credit-purchase/refund`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(refundData),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `Failed to process refund: ${response.status}`);
  }

  return await response.json();
}

/**
 * Récupère les statistiques de surveillance des paiements
 */
export async function getMonitoringStats(): Promise<MonitoringStats> {
  const token = getTokenFromCookie("idToken");
  
  if (!token) {
    throw new Error("No authentication token found");
  }

  const response = await fetch(`${BASE_API_URL}/credit-purchase/monitoring/stats`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `Failed to fetch monitoring stats: ${response.status}`);
  }

  return await response.json();
}

/**
 * Vérifie le statut d'un paiement spécifique
 */
export async function checkPaymentStatus(transactionId: string): Promise<any> {
  const token = getTokenFromCookie("idToken");
  
  if (!token) {
    throw new Error("No authentication token found");
  }

  const response = await fetch(`${BASE_API_URL}/credit-purchase/payment/${transactionId}/status`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `Failed to check payment status: ${response.status}`);
  }

  return await response.json();
}

/**
 * Formate le montant en devise
 */
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('fr-FR').format(amount);
}

/**
 * Formate la date
 */
export function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

/**
 * Calcule le nombre de jours depuis une date
 */
export function getDaysSince(dateString: string): number {
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

/**
 * Détermine la priorité d'une transaction
 */
export function getTransactionPriority(transaction: ProblematicTransaction): 'low' | 'medium' | 'high' {
  const daysPending = transaction.days_pending || getDaysSince(transaction.purchase_date);
  
  if (transaction.payment_status === 'failed') return 'high';
  if (daysPending > 2) return 'high';
  if (daysPending > 1) return 'medium';
  return 'low';
}

/**
 * Valide un numéro de téléphone camerounais
 */
export function validateCameroonianPhone(phone: string): boolean {
  const phoneRegex = /^6[0-9]{8}$/;
  return phoneRegex.test(phone);
}

/**
 * Valide les données de remboursement
 */
export function validateRefundData(data: Partial<RefundRequest>): string[] {
  const errors: string[] = [];

  if (!data.transaction_id) {
    errors.push("L'ID de transaction est requis");
  }

  if (!data.phone) {
    errors.push("Le numéro de téléphone est requis");
  } else if (!validateCameroonianPhone(data.phone)) {
    errors.push("Le numéro de téléphone doit être au format 6XXXXXXXX");
  }

  if (!data.reason || data.reason.trim().length < 10) {
    errors.push("La raison du remboursement doit contenir au moins 10 caractères");
  }

  if (data.amount && data.amount < 100) {
    errors.push("Le montant minimum est de 100 XAF");
  }

  return errors;
}
