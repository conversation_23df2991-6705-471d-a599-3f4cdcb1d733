"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
    CheckCircle,
    AlertCircle,
    Info,
    ArrowRight,
    ArrowLeft,
    Download,
    Settings,
    Users,
    GraduationCap,
    School,
    Shield,
    Zap,
} from "lucide-react";

export default function GettingStartedPage() {
    const router = useRouter();
    const [completedSteps, setCompletedSteps] = useState<number[]>([]);

    const toggleStep = (stepIndex: number) => {
        setCompletedSteps((prev) =>
            prev.includes(stepIndex)
                ? prev.filter((i) => i !== stepIndex)
                : [...prev, stepIndex]
        );
    };

    const setupSteps = [
        {
            title: "System Requirements",
            description: "Ensure your system meets the minimum requirements",
            content: [
                "Modern web browser (Chrome 90+, Firefox 88+, Safari 14+, Edge 90+)",
                "Stable internet connection (minimum 1 Mbps)",
                "JavaScript enabled in browser",
                "Minimum screen resolution: 1024x768",
            ],
        },
        {
            title: "Account Setup",
            description: "Create your school's Scholarify account",
            content: [
                "Contact our sales team or sign up for a free trial",
                "Receive your school's unique login credentials",
                "Complete the initial school profile setup",
                "Verify your email address and contact information",
            ],
        },
        {
            title: "School Configuration",
            description: "Configure your school's basic information",
            content: [
                "Enter school name, address, and contact details",
                "Set up academic year and term structure",
                "Configure school logo and branding",
                "Define school hours and working days",
            ],
        },
        {
            title: "User Management Setup",
            description: "Create user accounts for staff and students",
            content: [
                "Set up administrator accounts",
                "Create teacher accounts with appropriate permissions",
                "Import or manually add student records",
                "Generate parent access credentials",
            ],
        },
        {
            title: "Academic Structure",
            description: "Define your school's academic organization",
            content: [
                "Create class levels (Grade 1, Grade 2, etc.)",
                "Set up subjects for each class level",
                "Define class sections and student assignments",
                "Configure grading system and assessment methods",
            ],
        },
        {
            title: "Launch & Training",
            description: "Go live with your Scholarify system",
            content: [
                "Complete staff training sessions",
                "Run pilot tests with a small group",
                "Go live with full system",
                "Monitor and provide ongoing support",
            ],
        },
    ];

    const features = [
        {
            icon: <School className="w-6 h-6" />,
            title: "School Management",
            description:
                "Complete administrative control over your school operations",
        },
        {
            icon: <Users className="w-6 h-6" />,
            title: "User Management",
            description:
                "Manage students, teachers, parents, and staff efficiently",
        },
        {
            icon: <GraduationCap className="w-6 h-6" />,
            title: "Academic Tracking",
            description: "Monitor student progress and academic performance",
        },
        {
            icon: <Shield className="w-6 h-6" />,
            title: "Secure Platform",
            description: "Enterprise-grade security for all your data",
        },
    ];

    return (
        <div className="p-8">
            {/* Back Button */}
            <div className="mb-6">
                <button
                    onClick={() => router.back()}
                    className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-teal-600 dark:hover:text-teal-300 transition-colors group"
                >
                    <ArrowLeft className="w-4 h-4 transition-transform group-hover:-translate-x-1" />
                    <span className="text-sm font-medium">
                        Back to Documentation
                    </span>
                </button>
            </div>

            {/* Header */}
            <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                    Getting Started with Scholarify
                </h1>
                <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
                    Welcome to Scholarify! This guide will help you set up and
                    configure your school management system. Follow these steps
                    to get your school up and running quickly.
                </p>
            </div>

            {/* Quick Info Cards */}
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                {features.map((feature, index) => (
                    <div
                        key={index}
                        className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800/50"
                    >
                        <div className="text-teal-600 dark:text-teal-300 mb-2">
                            {feature.icon}
                        </div>
                        <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                            {feature.title}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                            {feature.description}
                        </p>
                    </div>
                ))}
            </div>

            {/* Important Notice */}
            <div className="flex items-start space-x-3 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg mb-8">
                <Info className="w-5 h-5 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
                <div>
                    <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-1">
                        Before You Begin
                    </h3>
                    <p className="text-blue-800 dark:text-blue-200 text-sm">
                        Make sure you have all necessary school information
                        ready, including student lists, staff details, and
                        academic structure. This will make the setup process
                        much smoother.
                    </p>
                </div>
            </div>

            {/* Setup Steps */}
            <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                    Setup Steps
                </h2>
                <div className="space-y-4">
                    {setupSteps.map((step, index) => (
                        <div
                            key={index}
                            className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden"
                        >
                            <button
                                onClick={() => toggleStep(index)}
                                className="w-full flex items-center justify-between p-6 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                            >
                                <div className="flex items-center space-x-4">
                                    <div
                                        className={`w-8 h-8 rounded-full flex items-center justify-center ${
                                            completedSteps.includes(index)
                                                ? "bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400"
                                                : "bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500"
                                        }`}
                                    >
                                        {completedSteps.includes(index) ? (
                                            <CheckCircle className="w-5 h-5" />
                                        ) : (
                                            <span className="text-sm font-semibold">
                                                {index + 1}
                                            </span>
                                        )}
                                    </div>
                                    <div className="text-left">
                                        <h3 className="font-semibold text-gray-900 dark:text-white">
                                            {step.title}
                                        </h3>
                                        <p className="text-sm text-gray-600 dark:text-gray-400">
                                            {step.description}
                                        </p>
                                    </div>
                                </div>
                                <ArrowRight
                                    className={`w-5 h-5 text-gray-400 transition-transform ${
                                        completedSteps.includes(index)
                                            ? "rotate-90"
                                            : ""
                                    }`}
                                />
                            </button>

                            {completedSteps.includes(index) && (
                                <div className="px-6 pb-6 bg-gray-50 dark:bg-gray-800/50">
                                    <ul className="space-y-2">
                                        {step.content.map((item, itemIndex) => (
                                            <li
                                                key={itemIndex}
                                                className="flex items-start space-x-2"
                                            >
                                                <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0 mt-0.5" />
                                                <span className="text-sm text-gray-700 dark:text-gray-300">
                                                    {item}
                                                </span>
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            )}
                        </div>
                    ))}
                </div>
            </div>

            {/* Next Steps */}
            <div className="bg-teal-50 dark:bg-teal-900/20 border border-teal-200 dark:border-teal-800 rounded-lg p-6">
                <h2 className="text-xl font-bold text-teal-900 dark:text-teal-100 mb-4">
                    What's Next?
                </h2>
                <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                        <Zap className="w-5 h-5 text-teal-600 dark:text-teal-300" />
                        <span className="text-teal-800 dark:text-teal-200">
                            Explore the <strong>User Management</strong> section
                            to learn about roles and permissions
                        </span>
                    </div>
                    <div className="flex items-center space-x-3">
                        <Zap className="w-5 h-5 text-teal-600 dark:text-teal-300" />
                        <span className="text-teal-800 dark:text-teal-200">
                            Check out the <strong>API Reference</strong> for
                            advanced integrations
                        </span>
                    </div>
                    <div className="flex items-center space-x-3">
                        <Zap className="w-5 h-5 text-teal-600 dark:text-teal-300" />
                        <span className="text-teal-800 dark:text-teal-200">
                            Contact our support team if you need assistance
                        </span>
                    </div>
                </div>
            </div>

            {/* Support Section */}
            <div className="mt-8 p-6 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                    Need Help?
                </h2>
                <div className="grid md:grid-cols-2 gap-4">
                    <div className="flex items-start space-x-3">
                        <AlertCircle className="w-5 h-5 text-orange-500 flex-shrink-0 mt-0.5" />
                        <div>
                            <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                                Technical Support
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                Email: <EMAIL>
                                <br />
                                Phone: +****************
                                <br />
                                Available 24/7
                            </p>
                        </div>
                    </div>
                    <div className="flex items-start space-x-3">
                        <Settings className="w-5 h-5 text-teal-500 flex-shrink-0 mt-0.5" />
                        <div>
                            <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                                Setup Assistance
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                Free setup consultation
                                <br />
                                Video training sessions
                                <br />
                                Custom implementation support
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
