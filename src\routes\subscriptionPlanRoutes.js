const express = require('express');
const subscriptionPlanController = require('../controllers/subscriptionPlanController');
const { authenticate, authorize } = require('../middleware/middleware');

const router = express.Router();

// Routes pour les plans de souscription

// GET /subscription-plans - Obtenir tous les plans actifs
router.get('/',
  subscriptionPlanController.getAllPlans
);

// GET /subscription-plans/types/list - Obtenir seulement les noms des types de plans
router.get('/types/list',
  subscriptionPlanController.getPlanTypes
);

// GET /subscription-plans/:plan_name - Obtenir un plan spécifique
router.get('/:plan_name',
  subscriptionPlanController.getPlanByName
);

// GET /subscription-plans/pricing/calculate - Calculer le prix
router.get('/pricing/calculate',
  subscriptionPlanController.calculatePrice
);

// GET /subscription-plans/compare/all - Comparer tous les plans
router.get('/compare/all',
  subscriptionPlanController.comparePlans
);

// PUT /subscription-plans/school/:school_id/change - Changer le plan d'une école
router.put('/school/:school_id/change',
  authenticate,
  authorize(['admin', 'super']),
  subscriptionPlanController.changePlan
);

// GET /subscription-plans/school/:school_id/recommendations - Recommandations de plan
router.get('/school/:school_id/recommendations',
  authenticate,
  authorize(['admin', 'super']),
  subscriptionPlanController.getPlanRecommendations
);

// POST /subscription-plans/initialize - Initialiser les plans par défaut (admin)
router.post('/initialize',
  authenticate,
  authorize(['super']),
  subscriptionPlanController.initializeDefaultPlans
);

// POST /subscription-plans - Créer un nouveau plan (super admin)
router.post('/',
  authenticate,
  authorize(['super']),
  subscriptionPlanController.createPlan
);

// PUT /subscription-plans/:plan_id - Modifier un plan existant (super admin)
router.put('/:plan_id',
  authenticate,
  authorize(['super']),
  subscriptionPlanController.updatePlan
);

// DELETE /subscription-plans/:plan_id - Supprimer un plan (super admin)
router.delete('/:plan_id',
  authenticate,
  authorize(['super']),
  subscriptionPlanController.deletePlan
);

module.exports = router;
