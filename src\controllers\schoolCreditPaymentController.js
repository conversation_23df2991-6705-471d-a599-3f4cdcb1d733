// controllers/schoolCreditPaymentController.js
const fapshi = require('../utils/fapshi');
const mongoose = require('mongoose');
const moment = require('moment');
const CreditPurchase = require('../models/CreditPurchase');
const SchoolSubscription = require('../models/SchoolSubscription');
const School = require('../models/School');
const User = require('../models/User');
const SubscriptionPlan = require('../models/SubscriptionPlan');

/**
 * Initiate credit purchase payment for school admins and authorized staff
 * This is specifically for school credit purchases, different from parent payments
 */
const initiateSchoolCreditPayment = async (req, res) => {
  try {
    const {
      school_id,
      credits_amount,
      payment_method = 'fapshi',
      billing_info = {},
      promotion_code,
      redirect_url
    } = req.body;

    const user = req.user; // User attached by authenticate middleware

    // Validation des données
    if (!school_id || !credits_amount || credits_amount < 1) {
      return res.status(400).json({ 
        message: 'School ID and valid credits amount are required' 
      });
    }

    // Vérifier que l'école existe
    const school = await School.findById(school_id);
    if (!school) {
      return res.status(404).json({ message: 'School not found' });
    }

    // Vérifier que l'utilisateur est associé à cette école
    if (!user.school_ids || !user.school_ids.includes(school_id)) {
      return res.status(403).json({ 
        message: 'User not associated with this school' 
      });
    }

    // Obtenir ou créer la souscription de l'école
    let subscription = await SchoolSubscription.findOne({ school_id });
    if (!subscription) {
      subscription = await SchoolSubscription.createDefaultSubscription(school_id);
    }

    // Obtenir le plan de souscription pour le prix
    const plan = await SubscriptionPlan.getPlanByName(subscription.plan_type);
    if (!plan) {
      return res.status(404).json({ message: 'Subscription plan not found' });
    }

    // Vérifier le minimum d'achat
    if (credits_amount < plan.minimum_purchase) {
      return res.status(400).json({ 
        message: `Minimum purchase is ${plan.minimum_purchase} credits` 
      });
    }

    // Calculer le prix
    const price_per_credit = plan.price_per_credit;
    let subtotal = credits_amount * price_per_credit;
    let discount_amount = 0;
    let discount_percentage = 0;

    // Appliquer le code promo si fourni
    if (promotion_code) {
      // TODO: Implémenter la logique de code promo
      // Pour l'instant, on ignore les codes promo
    }

    const total_amount = subtotal - discount_amount;

    // Vérifier le montant minimum de Fapshi (100 XAF)
    if (total_amount < 100) {
      return res.status(400).json({ 
        message: 'Minimum payment amount is 100 XAF' 
      });
    }

    // Générer les IDs de transaction
    const purchase_id = CreditPurchase.generatePurchaseId();
    const transaction_id = `SCR_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Créer l'enregistrement d'achat
    const creditPurchase = new CreditPurchase({
      school_id,
      subscription_id: subscription._id,
      transaction_id,
      purchase_id,
      credits_purchased: credits_amount,
      price_per_credit,
      total_amount,
      currency: 'XAF',
      payment_method,
      payment_status: 'pending',
      purchased_by: user._id,
      purchaser_email: user.email || billing_info.email,
      billing_info: {
        name: billing_info.name || user.name,
        email: billing_info.email || user.email,
        phone: billing_info.phone || user.phone,
        organization: school.name
      },
      promotion_code,
      discount_amount,
      discount_percentage
    });

    await creditPurchase.save();

    // Initier le paiement avec Fapshi
    let payment_response = {};
    
    if (payment_method === 'fapshi') {
      try {
        const fapshiPayload = {
          userId: user._id.toString(),
          amount: Math.round(total_amount), // Fapshi requires integer
          email: user.email || billing_info.email,
          externalId: purchase_id,
          redirectUrl: redirect_url || `${process.env.FRONTEND_URL}/school-admin/buy-credit/success`,
          message: `Achat de ${credits_amount} crédits pour ${school.name}`
        };

        console.log('Initiating Fapshi payment with payload:', fapshiPayload);
        payment_response = await fapshi.initiatePay(fapshiPayload);

        // Mettre à jour avec la réponse de Fapshi
        creditPurchase.payment_gateway_response = payment_response;
        await creditPurchase.save();

        // Vérifier si le paiement a été initié avec succès
        if (payment_response.statusCode !== 200) {
          await creditPurchase.markAsFailed(`Fapshi error: ${payment_response.message}`);
          
          return res.status(400).json({ 
            message: 'Payment initiation failed',
            error: payment_response.message,
            statusCode: payment_response.statusCode
          });
        }

      } catch (paymentError) {
        console.error('Payment initiation error:', paymentError);
        await creditPurchase.markAsFailed('Payment gateway error');
        
        return res.status(500).json({ 
          message: 'Payment initiation failed',
          error: paymentError.message 
        });
      }
    }

    // Réponse de succès
    res.status(200).json({
      purchase: {
        purchase_id: creditPurchase.purchase_id,
        transaction_id: creditPurchase.transaction_id,
        credits_purchased: creditPurchase.credits_purchased,
        total_amount: creditPurchase.total_amount,
        payment_status: creditPurchase.payment_status,
        currency: creditPurchase.currency
      },
      payment_response: payment_response,
      message: 'Credit purchase initiated successfully'
    });

  } catch (error) {
    console.error('Error initiating school credit payment:', error);
    res.status(500).json({ 
      message: 'Internal server error',
      error: error.message 
    });
  }
};

/**
 * Check payment status for school credit purchases
 */
const checkSchoolCreditPaymentStatus = async (req, res) => {
  try {
    const { transaction_id } = req.params;
    const user = req.user;

    if (!transaction_id) {
      return res.status(400).json({ message: 'Transaction ID is required' });
    }

    // Trouver l'achat de crédit
    const purchase = await CreditPurchase.findOne({ transaction_id })
      .populate('school_id', 'name')
      .populate('purchased_by', 'name email');

    if (!purchase) {
      return res.status(404).json({ message: 'Purchase not found' });
    }

    // Vérifier que l'utilisateur a accès à cette transaction
    if (user.role !== 'super' && 
        !user.school_ids.includes(purchase.school_id._id.toString()) &&
        purchase.purchased_by._id.toString() !== user._id.toString()) {
      return res.status(403).json({ message: 'Access denied' });
    }

    // Si le paiement est déjà complété, retourner le statut
    if (purchase.payment_status === 'completed') {
      return res.status(200).json({
        transaction_id: purchase.transaction_id,
        purchase_id: purchase.purchase_id,
        payment_status: purchase.payment_status,
        credits_purchased: purchase.credits_purchased,
        total_amount: purchase.total_amount,
        purchase_date: purchase.purchase_date,
        payment_completed_date: purchase.payment_completed_date,
        message: 'Payment completed successfully'
      });
    }

    // Vérifier le statut avec Fapshi si le paiement est en attente
    if (purchase.payment_status === 'pending' && purchase.payment_gateway_response?.transId) {
      try {
        const fapshiStatus = await fapshi.paymentStatus(purchase.payment_gateway_response.transId);
        
        // Mettre à jour le statut selon la réponse de Fapshi
        if (fapshiStatus.status === 'SUCCESSFUL') {
          await purchase.markAsCompleted(fapshiStatus);
        } else if (fapshiStatus.status === 'FAILED' || fapshiStatus.status === 'EXPIRED') {
          await purchase.markAsFailed(`Payment ${fapshiStatus.status.toLowerCase()}`);
        }
        
        // Recharger l'achat mis à jour
        await purchase.reload();
      } catch (fapshiError) {
        console.error('Error checking Fapshi status:', fapshiError);
        // Continue avec le statut actuel si Fapshi n'est pas disponible
      }
    }

    res.status(200).json({
      transaction_id: purchase.transaction_id,
      purchase_id: purchase.purchase_id,
      payment_status: purchase.payment_status,
      credits_purchased: purchase.credits_purchased,
      total_amount: purchase.total_amount,
      purchase_date: purchase.purchase_date,
      payment_completed_date: purchase.payment_completed_date,
      message: 'Payment status retrieved successfully'
    });

  } catch (error) {
    console.error('Error checking school credit payment status:', error);
    res.status(500).json({ 
      message: 'Internal server error',
      error: error.message 
    });
  }
};

/**
 * Handle Fapshi webhook for school credit purchases
 * This webhook is called by Fapshi when payment status changes
 */
const handleSchoolCreditWebhook = async (req, res) => {
  try {
    console.log('School credit webhook received:', req.body);

    // Vérifier le statut du paiement avec Fapshi pour s'assurer de la source
    const event = await fapshi.paymentStatus(req.body.transId);

    if (!event || event.statusCode !== 200) {
      console.error('Invalid webhook event:', event);
      return res.status(400).json({ message: 'Invalid webhook event' });
    }

    const { transId, status, email, amount, dateInitiated, userId, externalId } = event;

    // Trouver l'achat de crédit correspondant
    const purchase = await CreditPurchase.findOne({
      purchase_id: externalId
    }).populate('school_id subscription_id');

    if (!purchase) {
      console.error('Purchase not found for external ID:', externalId);
      return res.status(404).json({ message: 'Purchase not found' });
    }

    // Traiter selon le statut
    switch (status) {
      case 'SUCCESSFUL':
        if (purchase.payment_status !== 'completed') {
          console.log(`Processing successful payment for purchase ${purchase.purchase_id}`);

          // Marquer le paiement comme complété
          await purchase.markAsCompleted({
            transId,
            status,
            amount,
            dateInitiated,
            processedAt: new Date()
          });

          // La mise à jour des crédits de l'école se fait automatiquement
          // via le middleware post-save du modèle CreditPurchase

          console.log(`Successfully processed payment for ${purchase.credits_purchased} credits`);
        }
        break;

      case 'FAILED':
        if (purchase.payment_status === 'pending') {
          console.log(`Processing failed payment for purchase ${purchase.purchase_id}`);
          await purchase.markAsFailed(`Payment failed: ${event.message || 'Unknown error'}`);
        }
        break;

      case 'EXPIRED':
        if (purchase.payment_status === 'pending') {
          console.log(`Processing expired payment for purchase ${purchase.purchase_id}`);
          await purchase.markAsFailed('Payment expired');
        }
        break;

      default:
        console.log(`Unhandled payment status: ${status} for purchase ${purchase.purchase_id}`);
    }

    // Retourner une réponse 200 pour confirmer la réception du webhook
    res.status(200).json({
      message: 'Webhook processed successfully',
      purchase_id: purchase.purchase_id,
      status: purchase.payment_status
    });

  } catch (error) {
    console.error('Error handling school credit webhook:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error.message
    });
  }
};

module.exports = {
  initiateSchoolCreditPayment,
  checkSchoolCreditPaymentStatus,
  handleSchoolCreditWebhook
};
