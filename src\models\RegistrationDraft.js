// models/RegistrationDraft.js
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const RegistrationDraftSchema = new Schema({
    // User Identification - Now required since all users are logged in
    user_id: {
        type: Schema.Types.ObjectId,
        ref: 'User', // Reference to your User model
        required: true, // User ID is now mandatory
    },
    school_id: {
        type: Schema.Types.ObjectId,
        ref: 'School', // Reference to your School model
        required: true,
    },

    // Form Progress Tracking
    currentStep: {
        type: Number,
        default: 1, // Start at step 1
    },
    // lastUpdated will be handled by Mongoose `timestamps` automatically

    isCompleted: {
        type: Boolean,
        default: false,
    },
    status: {
        type: String,
        enum: ["not enrolled", "pending payment", "enrolled", "draft"],
        default: "draft",
    },

    // All your existing FormData fields from the frontend go here:
    firstName: { type: String, trim: true, default: "" },
    lastName: { type: String, trim: true, default: "" },
    middleName: { type: String, trim: true, default: "" },
    dateOfBirth: { type: Date, default: null }, // Changed to Date
    nationality: { type: String, trim: true, default: "" },
    gender: { type: String, enum: ["Male", "Female", "Other"], default: "" },
    place_of_birth: { type: String, trim: true, default: "" },
    address: { type: String, trim: true, default: "" },
    student_phone: { type: String, trim: true, default: "" },
    student_country_code: { type: String, trim: true, default: "" },
    guardian_address: { type: String, trim: true, default: "" },
    guardian_phone: { type: String, trim: true, default: "" },
    guardian_country_code: { type: String, trim: true, default: "" },
    guardian_name: { type: String, trim: true, default: "" },
    guardian_occupation: { type: String, trim: true, default: "" },
    guardian_email: { type: String, trim: true, default: "" },
    guardian_relationship: { type: String, trim: true, default: "" },
    emergency_contact_name: { type: String, trim: true, default: "" },
    emergency_contact_phone: { type: String, trim: true, default: "" },
    emergency_contact_country_code: { type: String, trim: true, default: "" },
    emergency_contact_relationship: { type: String, trim: true, default: "" },
    previous_school: { type: String, trim: true, default: "" },
    class_level: { type: String, trim: true, default: "" },
    guardian_agreed_to_terms: { type: Boolean, default: false },
    transcript_reportcard: { type: Boolean, default: false },
    health_condition: { type: String, trim: true, default: "" },
    doctors_name: { type: String, trim: true, default: "" },
    doctors_phone: { type: String, trim: true, default: "" },
    doctor_country_code: { type: String, trim: true, default: "" },
    registered: { type: Boolean, default: false },
    selectedFees: [{ type: String }],
    selectedResources: [{ type: String }],
    paymentMode: { type: String, enum: ["full", "installment"], default: "full" },
    installments: { type: Number, default: 1 },
    installmentDates: [{ type: Date }],
    applyScholarship: { type: Boolean, default: false },
    scholarshipAmount: { type: Number, default: 0 },
    scholarshipPercentage: { type: Number, default: 0 },
}, { timestamps: true }); // Mongoose adds `createdAt` and `updatedAt` fields automatically

// Now a simpler unique index: one draft per user per school
RegistrationDraftSchema.index({ userId: 1, schoolId: 1 }, { unique: true });

const RegistrationDraft = mongoose.models.RegistrationDraft || mongoose.model('RegistrationDraft', RegistrationDraftSchema);

module.exports = RegistrationDraft;
