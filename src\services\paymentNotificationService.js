const { sendEmail } = require('../utils/emailService');
const smsService = require('../utils/smsService');

/**
 * Service de notifications de paiement (Email + SMS)
 */
class PaymentNotificationService {
  
  /**
   * Envoyer une notification de paiement réussi
   * @param {Object} data - Données de paiement
   * @param {Object} options - Options de notification
   */
  static async sendPaymentSuccessNotification(data, options = {}) {
    const {
      email,
      phone,
      userName,
      schoolName,
      creditsAmount,
      totalAmount,
      currency = 'XAF',
      transactionId,
      purchaseDate
    } = data;

    const {
      sendEmail: enableEmail = true,
      sendSMS: enableSMS = true,
      priority = 'normal'
    } = options;

    const results = {
      email: null,
      sms: null,
      success: false
    };

    try {
      // Envoyer email de confirmation
      if (enableEmail && email) {
        try {
          const emailData = {
            to: email,
            subject: '✅ Confirmation d\'achat de crédits - Scholarify',
            template: 'payment-confirmation',
            data: {
              userName: userName || 'Cher client',
              schoolName: schoolName || 'Votre école',
              creditsAmount,
              totalAmount,
              currency,
              transactionId,
              purchaseDate: purchaseDate || new Date().toLocaleDateString('fr-FR'),
              dashboardUrl: `${process.env.FRONTEND_URL}/school-admin/dashboard`
            }
          };

          await sendEmail(emailData);
          results.email = { success: true, to: email };
          console.log(`📧 Email de confirmation envoyé à: ${email}`);

        } catch (error) {
          console.error('❌ Erreur envoi email confirmation:', error);
          results.email = { success: false, error: error.message, to: email };
        }
      }

      // Envoyer SMS de confirmation
      if (enableSMS && phone) {
        try {
          const smsResult = await smsService.sendPaymentSuccessSMS({
            phone: smsService.normalizePhoneNumber(phone),
            userName,
            creditsAmount,
            totalAmount,
            transactionId
          });

          results.sms = smsResult;
          if (smsResult.success) {
            console.log(`📱 SMS de confirmation envoyé à: ${phone}`);
          }

        } catch (error) {
          console.error('❌ Erreur envoi SMS confirmation:', error);
          results.sms = { success: false, error: error.message, to: phone };
        }
      }

      // Considérer comme succès si au moins une notification est envoyée
      results.success = (results.email?.success || results.sms?.success) || false;

      return results;

    } catch (error) {
      console.error('❌ Erreur notification paiement réussi:', error);
      return {
        email: null,
        sms: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Envoyer une notification d'échec de paiement
   * @param {Object} data - Données d'échec
   * @param {Object} options - Options de notification
   */
  static async sendPaymentFailureNotification(data, options = {}) {
    const {
      email,
      phone,
      userName,
      schoolName,
      creditsAmount,
      totalAmount,
      currency = 'XAF',
      transactionId,
      failureReason,
      canRetry = false,
      retryUrl
    } = data;

    const {
      sendEmail: enableEmail = true,
      sendSMS: enableSMS = true,
      priority = 'high'
    } = options;

    const results = {
      email: null,
      sms: null,
      success: false
    };

    try {
      // Envoyer email d'échec
      if (enableEmail && email) {
        try {
          const emailData = {
            to: email,
            subject: '❌ Échec de paiement - Scholarify',
            template: 'payment-failure',
            data: {
              userName: userName || 'Cher client',
              schoolName: schoolName || 'Votre école',
              creditsAmount,
              totalAmount,
              currency,
              transactionId,
              failureReason: failureReason || 'Une erreur est survenue',
              canRetry,
              retryUrl: retryUrl || `${process.env.FRONTEND_URL}/school-admin/buy-credit`,
              supportUrl: `${process.env.FRONTEND_URL}/support`
            }
          };

          await sendEmail(emailData);
          results.email = { success: true, to: email };
          console.log(`📧 Email d'échec envoyé à: ${email}`);

        } catch (error) {
          console.error('❌ Erreur envoi email échec:', error);
          results.email = { success: false, error: error.message, to: email };
        }
      }

      // Envoyer SMS d'échec
      if (enableSMS && phone) {
        try {
          const smsResult = await smsService.sendPaymentFailureSMS({
            phone: smsService.normalizePhoneNumber(phone),
            userName,
            reason: failureReason,
            transactionId
          });

          results.sms = smsResult;
          if (smsResult.success) {
            console.log(`📱 SMS d'échec envoyé à: ${phone}`);
          }

        } catch (error) {
          console.error('❌ Erreur envoi SMS échec:', error);
          results.sms = { success: false, error: error.message, to: phone };
        }
      }

      results.success = (results.email?.success || results.sms?.success) || false;
      return results;

    } catch (error) {
      console.error('❌ Erreur notification échec paiement:', error);
      return {
        email: null,
        sms: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Envoyer une notification d'expiration de paiement
   * @param {Object} data - Données d'expiration
   * @param {Object} options - Options de notification
   */
  static async sendPaymentExpirationNotification(data, options = {}) {
    const {
      email,
      phone,
      userName,
      schoolName,
      creditsAmount,
      totalAmount,
      currency = 'XAF',
      transactionId
    } = data;

    const {
      sendEmail: enableEmail = true,
      sendSMS: enableSMS = true
    } = options;

    const results = {
      email: null,
      sms: null,
      success: false
    };

    try {
      // Envoyer email d'expiration
      if (enableEmail && email) {
        try {
          const emailData = {
            to: email,
            subject: '⏰ Lien de paiement expiré - Scholarify',
            template: 'payment-expiration',
            data: {
              userName: userName || 'Cher client',
              schoolName: schoolName || 'Votre école',
              creditsAmount,
              totalAmount,
              currency,
              transactionId,
              newPurchaseUrl: `${process.env.FRONTEND_URL}/school-admin/buy-credit`
            }
          };

          await sendEmail(emailData);
          results.email = { success: true, to: email };
          console.log(`📧 Email d'expiration envoyé à: ${email}`);

        } catch (error) {
          console.error('❌ Erreur envoi email expiration:', error);
          results.email = { success: false, error: error.message, to: email };
        }
      }

      // Envoyer SMS d'expiration
      if (enableSMS && phone) {
        try {
          const smsResult = await smsService.sendPaymentExpirationSMS({
            phone: smsService.normalizePhoneNumber(phone),
            userName,
            transactionId
          });

          results.sms = smsResult;
          if (smsResult.success) {
            console.log(`📱 SMS d'expiration envoyé à: ${phone}`);
          }

        } catch (error) {
          console.error('❌ Erreur envoi SMS expiration:', error);
          results.sms = { success: false, error: error.message, to: phone };
        }
      }

      results.success = (results.email?.success || results.sms?.success) || false;
      return results;

    } catch (error) {
      console.error('❌ Erreur notification expiration:', error);
      return {
        email: null,
        sms: null,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Envoyer une notification de solde faible
   * @param {Object} data - Données de solde
   * @param {Object} options - Options de notification
   */
  static async sendLowCreditsNotification(data, options = {}) {
    const {
      email,
      phone,
      userName,
      schoolName,
      remainingCredits,
      threshold,
      recommendedPurchase
    } = data;

    const {
      sendEmail: enableEmail = true,
      sendSMS: enableSMS = true
    } = options;

    const results = {
      email: null,
      sms: null,
      success: false
    };

    try {
      // Envoyer email de solde faible
      if (enableEmail && email) {
        try {
          const emailData = {
            to: email,
            subject: '⚠️ Solde de crédits faible - Scholarify',
            template: 'low-credits-alert',
            data: {
              userName: userName || 'Cher client',
              schoolName: schoolName || 'Votre école',
              remainingCredits,
              threshold,
              recommendedPurchase: recommendedPurchase || Math.max(50, threshold * 2),
              purchaseUrl: `${process.env.FRONTEND_URL}/school-admin/buy-credit`
            }
          };

          await sendEmail(emailData);
          results.email = { success: true, to: email };
          console.log(`📧 Email solde faible envoyé à: ${email}`);

        } catch (error) {
          console.error('❌ Erreur envoi email solde faible:', error);
          results.email = { success: false, error: error.message, to: email };
        }
      }

      // Envoyer SMS de solde faible
      if (enableSMS && phone) {
        try {
          const smsResult = await smsService.sendLowCreditsSMS({
            phone: smsService.normalizePhoneNumber(phone),
            schoolName,
            remainingCredits,
            threshold
          });

          results.sms = smsResult;
          if (smsResult.success) {
            console.log(`📱 SMS solde faible envoyé à: ${phone}`);
          }

        } catch (error) {
          console.error('❌ Erreur envoi SMS solde faible:', error);
          results.sms = { success: false, error: error.message, to: phone };
        }
      }

      results.success = (results.email?.success || results.sms?.success) || false;
      return results;

    } catch (error) {
      console.error('❌ Erreur notification solde faible:', error);
      return {
        email: null,
        sms: null,
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = PaymentNotificationService;
