"use client";

import React, { useEffect, useState } from "react";
import { SchoolResourceSchema, SchoolResourceCreateSchema } from "@/app/models/SchoolResources";
import { Loader2, ChevronDown } from "lucide-react";

interface ClassLevel {
  _id: string;
  name: string;
}

export type SchoolResourceType =
  | 'Textbook'
  | 'Lab Material'
  | 'Notebook'
  | 'Uniform'
  | 'Stationery'
  | 'Sports Equipment'
  | 'Arts & Crafts'
  | 'Electronics'
  | 'Exam Material'
  | 'Miscellaneous'
  | 'Other';

const resourceTypes: SchoolResourceType[] = [
  'Textbook',
  'Lab Material',
  'Notebook',
  'Uniform',
  'Stationery',
  'Sports Equipment',
  'Arts & Crafts',
  'Electronics',
  'Exam Material',
  'Miscellaneous',
  'Other'
];

interface SchoolResourceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: SchoolResourceCreateSchema) => Promise<void>;
  resource?: SchoolResourceSchema | null;
  isSubmitting?: boolean;
  classLevels?: ClassLevel[];
}

const SchoolResourceModal: React.FC<SchoolResourceModalProps> = ({
  isOpen,
  onClose,
  onSave,
  resource,
  isSubmitting = false,
  classLevels = [],
}) => {
  const [formData, setFormData] = useState<SchoolResourceCreateSchema>({
    name: "",
    description: "",
    type: "Textbook",
    price: 0,
    stock: 0,
    class_level: "",
  });

  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  useEffect(() => {
    if (resource) {
      setFormData({
        name: resource.name,
        description: resource.description,
        type: resource.type as SchoolResourceType,
        price: resource.price,
        stock: resource.stock,
        class_level: resource.class_level || "",
      });
    } else {
      setFormData({
        name: "",
        description: "",
        type: "Textbook",
        price: 0,
        stock: 0,
        class_level: "",
      });
    }
    setErrors({});
  }, [resource]);

  const handleInputChange = (field: keyof SchoolResourceCreateSchema, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const validate = () => {
    const newErrors: { [key: string]: string } = {};
    if (!formData.name.trim()) newErrors.name = "Name is required";
    if (!formData.type.trim()) newErrors.type = "Type is required";
    if (formData.price < 0) newErrors.price = "Price cannot be negative";
    if (formData.stock < 0) newErrors.stock = "Stock cannot be negative";
    return newErrors;
  };

  const handleSubmit = async () => {
    const validationErrors = validate();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }
    await onSave(formData);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 px-4">
      <div className="bg-widget rounded-xl p-6 w-full max-w-2xl shadow-xl relative">
        <h2 className="text-xl font-bold text-text mb-6">
          {resource ? "Edit School Resource" : "Add New School Resource"}
        </h2>

        <div className="space-y-4">
          {/* Name */}
          <div>
            <label className="block text-sm mb-1 font-medium text-text">Resource Name</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              className={`w-full px-3 py-2 border rounded-md bg-widget text-text focus:outline-none focus:ring-2 focus:ring-teal-500 ${
                errors.name ? "border-red-500" : "border-stroke"
              }`}
              disabled={isSubmitting}
            />
            {errors.name && <p className="text-red-500 text-sm">{errors.name}</p>}
          </div>

          {/* Type */}
          <div className="relative">
            <label className="block text-sm mb-1 font-medium text-text">Type</label>
            <select
              value={formData.type}
              onChange={(e) => handleInputChange("type", e.target.value)}
              className={`w-full appearance-none px-3 py-2 border rounded-md bg-widget text-text focus:outline-none focus:ring-2 focus:ring-teal-500 ${
                errors.type ? "border-red-500" : "border-stroke"
              }`}
              disabled={isSubmitting}
            >
              {resourceTypes.map((type) => (
                <option key={type} value={type}>
                  {type}
                </option>
              ))}
            </select>
            <ChevronDown
              size={16}
              className="absolute right-3 top-9 pointer-events-none text-gray-500 dark:text-gray-400"
            />
            {errors.type && <p className="text-red-500 text-sm">{errors.type}</p>}
          </div>

          {/* Price */}
          <div>
            <label className="block text-sm mb-1 font-medium text-text">Price</label>
            <input
              type="number"
              value={formData.price}
              onChange={(e) => handleInputChange("price", parseFloat(e.target.value))}
              className={`w-full px-3 py-2 border rounded-md bg-widget text-text focus:outline-none focus:ring-2 focus:ring-teal-500 ${
                errors.price ? "border-red-500" : "border-stroke"
              }`}
              disabled={isSubmitting}
            />
            {errors.price && <p className="text-red-500 text-sm">{errors.price}</p>}
          </div>

          {/* Stock */}
          <div>
            <label className="block text-sm mb-1 font-medium text-text">Stock</label>
            <input
              type="number"
              value={formData.stock}
              onChange={(e) => handleInputChange("stock", parseInt(e.target.value))}
              className={`w-full px-3 py-2 border rounded-md bg-widget text-text focus:outline-none focus:ring-2 focus:ring-teal-500 ${
                errors.stock ? "border-red-500" : "border-stroke"
              }`}
              disabled={isSubmitting}
            />
            {errors.stock && <p className="text-red-500 text-sm">{errors.stock}</p>}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm mb-1 font-medium text-text">Description (optional)</label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-stroke rounded-md bg-widget text-text focus:outline-none focus:ring-2 focus:ring-teal-500"
              disabled={isSubmitting}
            />
          </div>

          {/* Class Level */}
          <div className="relative">
            <label className="block text-sm mb-1 font-medium text-text">Class Level</label>
            <select
              value={formData.class_level}
              onChange={(e) => handleInputChange("class_level", e.target.value)}
              className="w-full appearance-none px-3 py-2 border border-stroke rounded-md bg-widget text-text focus:outline-none focus:ring-2 focus:ring-teal-500"
              disabled={isSubmitting}
            >
              <option value="">Select class level</option>
              {classLevels.map((level) => (
                <option key={level._id} value={level._id}>
                  {level.name}
                </option>
              ))}
            </select>
            <ChevronDown
              size={16}
              className="absolute right-3 top-9 pointer-events-none text-gray-500 dark:text-gray-400"
            />
          </div>
        </div>

        {/* Footer Buttons */}
        <div className="mt-6 flex justify-end gap-3">
          <button
            onClick={onClose}
            className="px-4 py-2 rounded-md text-sm font-medium bg-gray-300 text-black hover:bg-gray-400 disabled:opacity-50"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            className="px-4 py-2 rounded-md text-sm font-medium bg-teal text-white hover:bg-teal-700 disabled:opacity-50 flex items-center"
            disabled={isSubmitting}
          >
            {isSubmitting && <Loader2 className="animate-spin w-4 h-4 mr-2" />}
            {resource ? "Update" : "Create"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default SchoolResourceModal;
