import React from 'react';

const SchoolResourceSkeleton: React.FC = () => {
  return (
    <div className="container mx-auto p-4 animate-pulse">
      <div className="h-8 bg-gray-300 dark:bg-gray-700 rounded w-1/3 mb-6"></div> {/* Title */}

      <div className="h-10 bg-gray-300 dark:bg-gray-700 rounded w-48 mb-4"></div> {/* Add button */}

      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-4">
          {/* Table Headers */}
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
          ))}
        </div>
        {/* Table Rows */}
        {[...Array(7)].map((_, i) => (
          <div key={i} className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 py-3 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 ml-auto"></div> {/* Actions placeholder */}
          </div>
        ))}
      </div>
    </div>
  );
};

export default SchoolResourceSkeleton;
