"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, 
  Check, 
  ArrowRight, 
  CreditCard, 
  AlertCircle, 
  CheckCircle,
  Loader2,
  Star,
  Zap,
  Users,
  MessageCircle
} from 'lucide-react';
import { SubscriptionPlanSchema, SchoolSubscriptionSchema } from '@/app/models/SchoolSubscriptionModel';
import { getSchoolSubscription, updateSchoolSubscription } from '@/app/services/SubscriptionServices';
import useAuth from '@/app/hooks/useAuth';
import CreditPurchaseModal from '@/components/modals/CreditPurchaseModal';
import { useFapshiPayment } from '@/hooks/useFapshiPayment';

interface PlanSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedPlan: SubscriptionPlanSchema;
  onSuccess?: () => void;
}

export default function PlanSelectionModal({
  isOpen,
  onClose,
  selectedPlan,
  onSuccess
}: PlanSelectionModalProps) {
  const { user } = useAuth();
  const [currentSubscription, setCurrentSubscription] = useState<SchoolSubscriptionSchema | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [step, setStep] = useState<'compare' | 'purchase' | 'processing' | 'success'>('compare');
  const [showCreditPurchase, setShowCreditPurchase] = useState(false);
  const [creditsToPurchase, setCreditsToPurchase] = useState(100); // Crédits par défaut
  const [chatbotCredits, setChatbotCredits] = useState(0); // Crédits chatbot
  const [billingInfo, setBillingInfo] = useState({
    name: '',
    email: '',
    phone: '',
    organization: ''
  });

  // Hook Fapshi pour gérer les paiements
  const fapshiPayment = useFapshiPayment({
    onSuccess: (data) => {
      console.log('Payment successful:', data);
      setStep('success');
      setTimeout(() => {
        if (onSuccess) onSuccess();
        handleClose();
      }, 2000);
    },
    onError: (error) => {
      console.error('Payment error:', error);
      setError('Erreur lors du paiement');
      setStep('purchase');
    },
    autoCheckPending: true
  });

  const schoolId = user?.school_ids?.[0];

  useEffect(() => {
    if (isOpen && schoolId) {
      fetchCurrentSubscription();
      // Initialiser les crédits par défaut selon le plan
      setCreditsToPurchase(100); // 100 crédits par défaut
      if (selectedPlan.plan_name === 'standard' || selectedPlan.plan_name === 'custom') {
        setChatbotCredits(10); // 10 crédits chatbot par défaut pour les plans avec chatbot
      } else {
        setChatbotCredits(0);
      }

      // Initialiser les informations de facturation avec les données utilisateur
      setBillingInfo({
        name: user?.name || '',
        email: user?.email || '',
        phone: user?.phone || '',
        organization: (user?.school_name as string) || ''
      });
    }
  }, [isOpen, schoolId, selectedPlan.plan_name, user]);

  const fetchCurrentSubscription = async () => {
    if (!schoolId) {
      console.warn('No schoolId available for fetching subscription');
      return;
    }

    try {
      setLoading(true);
      console.log('Fetching subscription for school:', schoolId);
      const subscription = await getSchoolSubscription(schoolId);
      console.log('Subscription response:', subscription);

      if (subscription) {
        setCurrentSubscription(subscription);
        console.log('Current subscription set:', subscription);
      } else {
        console.warn('No subscription data received');
        setCurrentSubscription(null);
      }
    } catch (err) {
      console.error('Error fetching current subscription:', err);
      setError('Impossible de charger les informations de souscription');
    } finally {
      setLoading(false);
    }
  };

  const handleChangePlan = () => {
    // Passer à l'étape d'achat de crédits au lieu de changer directement le plan
    setStep('purchase');
  };

  const handleConfirmPlanChange = async () => {
    if (!schoolId) return;

    try {
      setLoading(true);
      setError(null);
      setStep('processing');

      // 1. D'abord changer le plan
      const updates = {
        plan_type: selectedPlan.plan_name
      };

      await updateSchoolSubscription(schoolId, updates);

      // 2. Initier le paiement pour les crédits via Fapshi
      const totalCredits = creditsToPurchase + chatbotCredits;

      if (totalCredits > 0) {
        await fapshiPayment.initiatePayment({
          school_id: schoolId,
          credits_amount: totalCredits,
          billing_info: {
            name: user?.name || 'École',
            email: user?.email || '',
            phone: user?.phone || '',
            organization: user?.school_name || 'École'
          },
          redirect_url: `${window.location.origin}/pricing/success`
        });
      } else {
        // Si aucun crédit à acheter, juste confirmer le changement de plan
        setStep('success');
        setTimeout(() => {
          if (onSuccess) onSuccess();
          handleClose();
        }, 2000);
      }
    } catch (err: any) {
      setError(err.message || 'Erreur lors du changement de plan');
      setStep('purchase');
    } finally {
      setLoading(false);
    }
  };

  const handleBuyCredits = () => {
    setShowCreditPurchase(true);
  };

  const handleClose = () => {
    setStep('compare');
    setError(null);
    onClose();
  };

  const getPlanIcon = (planName: string) => {
    switch (planName) {
      case 'basic':
        return <Users className="h-6 w-6" />;
      case 'standard':
        return <Zap className="h-6 w-6" />;
      case 'custom':
        return <Star className="h-6 w-6" />;
      default:
        return <Check className="h-6 w-6" />;
    }
  };

  const getComparisonData = () => {
    if (!currentSubscription) return null;

    const currentPlanName = currentSubscription.plan_type;
    const isUpgrade = (selectedPlan.price_per_credit || 3000) > (currentSubscription.credits_balance > 0 ? 3000 : 0); // Simplified logic
    const isSamePlan = currentPlanName === selectedPlan.plan_name;

    return {
      currentPlanName,
      isUpgrade,
      isSamePlan,
      creditsBalance: currentSubscription.credits_balance
    };
  };

  if (!isOpen) return null;

  return (
    <>
      <AnimatePresence>
        <div className="fixed inset-0 z-[9999] overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-gray-500 dark:bg-gray-900 bg-opacity-75 dark:bg-opacity-80 transition-opacity"
              onClick={handleClose}
            />

            {/* Modal */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1}}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full"
            >
              {/* Header */}
              <div className="bg-white dark:bg-gray-800 px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                    {getPlanIcon(selectedPlan.plan_name)}
                    <span className="ml-2">Sélection de plan</span>
                  </h3>
                  <button
                    onClick={handleClose}
                    className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
                  >
                    <X className="h-6 w-6" />
                  </button>
                </div>
              </div>

              {/* Content */}
              <div className="bg-white dark:bg-gray-800 px-6 py-4">
                {step === 'compare' && (
                  <div className="space-y-6">
                    {/* Selected Plan Info */}
                    <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-100 dark:border-blue-800">
                      <div className="flex items-center mb-3">
                        {getPlanIcon(selectedPlan.plan_name)}
                        <div className="ml-3">
                          <h4 className="text-lg font-semibold text-blue-900 dark:text-blue-300">
                            {selectedPlan.display_name}
                          </h4>
                          <p className="text-blue-700 dark:text-blue-400 text-sm">
                            {selectedPlan.description}
                          </p>
                        </div>
                      </div>
                      
                      <div className="text-2xl font-bold text-blue-900 dark:text-blue-100 mb-2">
                        {(selectedPlan.price_per_credit || 3000).toLocaleString()} FCFA
                        <span className="text-sm font-normal text-blue-700 dark:text-blue-300">/crédit</span>
                      </div>
                    </div>

                    {/* Comparison with current plan */}
                    {loading ? (
                      <div className="flex items-center justify-center p-4">
                        <Loader2 className="h-6 w-6 animate-spin text-blue-500" />
                        <span className="ml-2 text-gray-600 dark:text-gray-400">Chargement de votre souscription...</span>
                      </div>
                    ) : getComparisonData() ? (
                      <div className="space-y-4">
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          Comparaison avec votre plan actuel
                        </h4>
                        
                        {getComparisonData()?.isSamePlan ? (
                          <div className="flex items-center p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                            <AlertCircle className="h-5 w-5 text-yellow-500 dark:text-yellow-400 mr-2" />
                            <span className="text-yellow-700 dark:text-yellow-300 text-sm">
                              Vous avez déjà ce plan. Vous pouvez acheter des crédits supplémentaires.
                            </span>
                          </div>
                        ) : (
                          <div className="grid grid-cols-2 gap-4">
                            <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                              <h5 className="font-medium text-gray-900 dark:text-white mb-2">Plan actuel</h5>
                              <p className="text-sm text-gray-600 dark:text-gray-400 capitalize">
                                {getComparisonData()?.currentPlanName}
                              </p>
                              <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                                Crédits restants: {getComparisonData()?.creditsBalance}
                              </p>
                            </div>
                            <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                              <h5 className="font-medium text-blue-900 dark:text-blue-300 mb-2">Nouveau plan</h5>
                              <p className="text-sm text-blue-700 dark:text-blue-400">
                                {selectedPlan.display_name}
                              </p>
                              <p className="text-xs text-blue-600 dark:text-blue-500 mt-1">
                                {(selectedPlan.price_per_credit || 3000).toLocaleString()} FCFA/crédit
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg">
                        <AlertCircle className="h-5 w-5 text-gray-500 dark:text-gray-400 mr-2" />
                        <span className="text-gray-600 dark:text-gray-400 text-sm">
                          Impossible de charger les informations de votre plan actuel
                        </span>
                      </div>
                    )}

                    {/* Features */}
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                        Fonctionnalités incluses
                      </h4>
                      <div className="grid grid-cols-1 gap-2">
                        {selectedPlan.features?.slice(0, 5).map((feature, index) => (
                          <div key={index} className="flex items-center text-sm">
                            <Check className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                            <span className="text-gray-700 dark:text-gray-300">{feature.description}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Error Message */}
                    {error && (
                      <div className="flex items-center p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                        <AlertCircle className="h-5 w-5 text-red-500 dark:text-red-400 mr-2" />
                        <span className="text-red-700 dark:text-red-300 text-sm">{error}</span>
                      </div>
                    )}
                  </div>
                )}

                {step === 'purchase' && (
                  <div className="space-y-6">
                    <div className="text-center">
                      <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        Achat de crédits pour {selectedPlan.display_name}
                      </h4>
                      <p className="text-gray-600 dark:text-gray-400">
                        Choisissez le nombre de crédits à acheter avec votre nouveau plan
                      </p>
                    </div>

                    {/* Crédits principaux */}
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Crédits principaux (gestion des étudiants)
                        </label>
                        <input
                          type="number"
                          min="1"
                          value={creditsToPurchase}
                          onChange={(e) => setCreditsToPurchase(parseInt(e.target.value) || 100)}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                        />
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          Coût: {(creditsToPurchase * (selectedPlan.price_per_credit || 3000)).toLocaleString()} FCFA
                        </p>
                      </div>

                      {/* Crédits chatbot (si applicable) */}
                      {(selectedPlan.plan_name === 'standard' || selectedPlan.plan_name === 'custom') && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Crédits chatbot IA (optionnel)
                          </label>
                          <input
                            type="number"
                            min="0"
                            value={chatbotCredits}
                            onChange={(e) => setChatbotCredits(parseInt(e.target.value) || 0)}
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                          />
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            Coût: {(chatbotCredits * (selectedPlan.price_per_credit || 3000)).toLocaleString()} FCFA
                          </p>
                        </div>
                      )}

                      {/* Total */}
                      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-100 dark:border-blue-800">
                        <div className="flex justify-between items-center">
                          <span className="font-medium text-blue-900 dark:text-blue-300">Total à payer:</span>
                          <span className="text-xl font-bold text-blue-900 dark:text-blue-100">
                            {((creditsToPurchase + chatbotCredits) * (selectedPlan.price_per_credit || 3000)).toLocaleString()} FCFA
                          </span>
                        </div>
                        <p className="text-xs text-blue-700 dark:text-blue-400 mt-1">
                          {creditsToPurchase + chatbotCredits} crédits au total
                        </p>
                      </div>
                    </div>

                    {/* Informations de facturation */}
                    <div className="space-y-4">
                      <h5 className="font-medium text-gray-900 dark:text-white">
                        Informations de facturation
                      </h5>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Nom complet *
                          </label>
                          <input
                            type="text"
                            value={billingInfo.name}
                            onChange={(e) => setBillingInfo(prev => ({ ...prev, name: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                            placeholder="Nom complet"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Email *
                          </label>
                          <input
                            type="email"
                            value={billingInfo.email}
                            onChange={(e) => setBillingInfo(prev => ({ ...prev, email: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                            placeholder="<EMAIL>"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Téléphone
                          </label>
                          <input
                            type="tel"
                            value={billingInfo.phone}
                            onChange={(e) => setBillingInfo(prev => ({ ...prev, phone: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                            placeholder="+237 6XX XXX XXX"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Organisation
                          </label>
                          <input
                            type="text"
                            value={billingInfo.organization}
                            onChange={(e) => setBillingInfo(prev => ({ ...prev, organization: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                            placeholder="Nom de l'école"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Error Message */}
                    {error && (
                      <div className="flex items-center p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                        <AlertCircle className="h-5 w-5 text-red-500 dark:text-red-400 mr-2" />
                        <span className="text-red-700 dark:text-red-300 text-sm">{error}</span>
                      </div>
                    )}
                  </div>
                )}

                {step === 'processing' && (
                  <div className="text-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-blue-600 dark:text-blue-400 mx-auto mb-4" />
                    <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                      Changement de plan en cours...
                    </h4>
                    <p className="text-gray-600 dark:text-gray-400">
                      Veuillez patienter pendant la mise à jour
                    </p>
                  </div>
                )}

                {step === 'success' && (
                  <div className="text-center py-8">
                    <CheckCircle className="h-12 w-12 text-green-500 dark:text-green-400 mx-auto mb-4" />
                    <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                      Plan mis à jour avec succès !
                    </h4>
                    <p className="text-gray-600 dark:text-gray-400">
                      Votre nouveau plan est maintenant actif
                    </p>
                  </div>
                )}
              </div>

              {/* Footer */}
              {step === 'compare' && (
                <div className="bg-gray-50 dark:bg-gray-700 px-6 py-4 flex justify-end space-x-3 border-t border-gray-200 dark:border-gray-600">
                  <button
                    onClick={handleClose}
                    className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
                  >
                    Annuler
                  </button>
                  
                  {getComparisonData()?.isSamePlan ? (
                    <button
                      onClick={handleBuyCredits}
                      className="px-6 py-2 bg-blue-600 dark:bg-blue-500 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors flex items-center"
                    >
                      <CreditCard className="h-4 w-4 mr-2" />
                      Acheter des crédits
                    </button>
                  ) : (
                    <button
                      onClick={handleChangePlan}
                      disabled={loading}
                      className="px-6 py-2 bg-blue-600 dark:bg-blue-500 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
                    >
                      {loading && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
                      Changer de plan
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </button>
                  )}
                </div>
              )}

              {/* Footer pour l'étape purchase */}
              {step === 'purchase' && (
                <div className="bg-gray-50 dark:bg-gray-700 px-6 py-4 flex justify-end space-x-3 border-t border-gray-200 dark:border-gray-600">
                  <button
                    onClick={() => setStep('compare')}
                    className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
                  >
                    Retour
                  </button>

                  <button
                    onClick={handleConfirmPlanChange}
                    disabled={loading || !billingInfo.name || !billingInfo.email || fapshiPayment.isInitiating}
                    className="px-6 py-2 bg-blue-600 dark:bg-blue-500 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
                  >
                    {(loading || fapshiPayment.isInitiating) && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
                    {fapshiPayment.isInitiating ? 'Redirection vers Fapshi...' : 'Confirmer et payer'}
                    {!fapshiPayment.isInitiating && <CreditCard className="h-4 w-4 ml-2" />}
                  </button>
                </div>
              )}
            </motion.div>
          </div>
        </div>
      </AnimatePresence>

      {/* Credit Purchase Modal */}
      {showCreditPurchase && schoolId && (
        <CreditPurchaseModal
          isOpen={showCreditPurchase}
          onClose={() => setShowCreditPurchase(false)}
          onSuccess={() => {
            setShowCreditPurchase(false);
            if (onSuccess) onSuccess();
            handleClose();
          }}
          schoolId={schoolId}
          currentPlan={selectedPlan.plan_name}
        />
      )}
    </>
  );
}
