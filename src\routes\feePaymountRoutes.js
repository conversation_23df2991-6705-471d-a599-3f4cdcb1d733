const express = require('express');
const { authenticate, authorize } = require('../middleware/middleware');

const feePaymentController = require('../controllers/feePaymentController');

const router = express.Router();

// Test route for Fee Payment
router.get('/test', feePaymentController.testFeePayment);

// GET all fee payments
router.get('/get-fee-payments', authenticate, feePaymentController.getAllFeePayments);

// GET a fee payment by ID
router.get('/get-fee-payment/:id', authenticate, authorize(['admin', 'super', 'school_admin', 'bursar']), feePaymentController.getFeePaymentById);

// POST to create a new fee payment
router.post('/create-fee-payment', authenticate, authorize(['admin', 'super', 'school_admin', 'bursar']), feePaymentController.createFeePayment);

// PUT to update a fee payment by ID
router.put('/update-fee-payment/:id', authenticate,authorize(['admin', 'super', 'school_admin', 'bursar']), feePaymentController.updateFeePayment);

// DELETE to remove a fee payment by ID
router.delete('/delete-fee-payment/:id', authenticate,authorize(['admin', 'super', 'school_admin', 'bursar']), feePaymentController.deleteFeePayment);

// GET fee payments by school ID
router.get('/get-fee-payments-by-school/:school_id', authenticate, authorize(['admin', 'super', 'school_admin', 'bursar']),feePaymentController.getFeePaymentsBySchoolId);

// GET fee payments by student ID
router.get('/get-fee-payments-by-student/:student_id', authenticate,authorize(['admin', 'super', 'school_admin', 'bursar']), feePaymentController.getFeePaymentsByStudentId);

module.exports = router;
