"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  X,
  AlertTriangle,
  DollarSign,
  Phone,
  User,
  CreditCard,
  CheckCircle,
  Loader2
} from 'lucide-react';
import { processRefund } from '@/app/services/RefundServices';

interface Transaction {
  _id: string;
  transaction_id: string;
  purchase_id: string;
  school_id: {
    _id: string;
    name: string;
    email?: string;
    phone?: string;
  };
  credits_purchased: number;
  total_amount: number;
  payment_status: string;
  billing_info: {
    name?: string;
    email?: string;
    phone?: string;
  };
  purchase_date: string;
}

interface RefundModalProps {
  transaction: Transaction;
  onClose: () => void;
  onSuccess: () => void;
}

export default function RefundModal({ transaction, onClose, onSuccess }: RefundModalProps) {
  const [step, setStep] = useState<'form' | 'processing' | 'success' | 'error'>('form');
  const [formData, setFormData] = useState({
    phone: transaction.billing_info.phone || transaction.school_id.phone || '',
    reason: '',
    amount: transaction.total_amount,
    medium: 'mobile money' as 'mobile money' | 'orange money'
  });
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.phone || !formData.reason) {
      setError('Veuillez remplir tous les champs obligatoires');
      return;
    }

    // Validation du numéro de téléphone
    const phoneRegex = /^6[0-9]{8}$/;
    if (!phoneRegex.test(formData.phone)) {
      setError('Le numéro de téléphone doit être au format 6XXXXXXXX');
      return;
    }

    setStep('processing');
    setError(null);

    try {
      const refundData = {
        transaction_id: transaction.transaction_id,
        phone: formData.phone,
        reason: formData.reason,
        amount: formData.amount,
        medium: formData.medium
      }
      const response = await processRefund(refundData);
      
      setStep('success');
      setTimeout(() => {
        onSuccess();
      }, 2000);
    } catch (err) {
      console.error('Refund error:', err);
      setError('Erreur de connexion. Veuillez réessayer.');
      setStep('error');
    }
  };

  const handleClose = () => {
    if (step !== 'processing') {
      onClose();
    }
  };

  const renderForm = () => (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Transaction Info */}
      <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
        <h4 className="font-medium text-gray-900 dark:text-white mb-3">Détails de la transaction</h4>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600 dark:text-gray-400">École:</span>
            <p className="font-medium text-gray-900 dark:text-white">{transaction.school_id.name}</p>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">Transaction ID:</span>
            <p className="font-medium text-gray-900 dark:text-white">{transaction.transaction_id}</p>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">Montant original:</span>
            <p className="font-medium text-gray-900 dark:text-white">{transaction.total_amount.toLocaleString()} XAF</p>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">Crédits:</span>
            <p className="font-medium text-gray-900 dark:text-white">{transaction.credits_purchased}</p>
          </div>
        </div>
      </div>

      {/* Refund Form */}
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            <Phone className="inline h-4 w-4 mr-1" />
            Numéro de téléphone pour le remboursement *
          </label>
          <input
            type="text"
            value={formData.phone}
            onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
            placeholder="6XXXXXXXX"
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            required
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Format: 6XXXXXXXX (numéro camerounais)
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Type de compte
          </label>
          <select
            value={formData.medium}
            onChange={(e) => setFormData({ ...formData, medium: e.target.value as 'mobile money' | 'orange money' })}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
          >
            <option value="mobile money">Mobile Money</option>
            <option value="orange money">Orange Money</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            <DollarSign className="inline h-4 w-4 mr-1" />
            Montant du remboursement (XAF)
          </label>
          <input
            type="number"
            value={formData.amount}
            onChange={(e) => setFormData({ ...formData, amount: parseInt(e.target.value) || 0 })}
            min="100"
            max={transaction.total_amount}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            required
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Maximum: {transaction.total_amount.toLocaleString()} XAF
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Raison du remboursement *
          </label>
          <textarea
            value={formData.reason}
            onChange={(e) => setFormData({ ...formData, reason: e.target.value })}
            placeholder="Décrivez la raison du remboursement..."
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            required
          />
        </div>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
            <p className="text-red-700 dark:text-red-400">{error}</p>
          </div>
        </div>
      )}

      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
        <button
          type="button"
          onClick={handleClose}
          className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
        >
          Annuler
        </button>
        <button
          type="submit"
          className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2"
        >
          <DollarSign className="h-4 w-4" />
          <span>Effectuer le remboursement</span>
        </button>
      </div>
    </form>
  );

  const renderProcessing = () => (
    <div className="text-center py-8">
      <Loader2 className="h-12 w-12 text-blue-500 mx-auto mb-4 animate-spin" />
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
        Traitement du remboursement...
      </h3>
      <p className="text-gray-600 dark:text-gray-400">
        Veuillez patienter pendant que nous traitons votre demande de remboursement.
      </p>
    </div>
  );

  const renderSuccess = () => (
    <div className="text-center py-8">
      <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
        Remboursement effectué avec succès !
      </h3>
      <p className="text-gray-600 dark:text-gray-400">
        Le remboursement a été envoyé vers le numéro {formData.phone}.
      </p>
    </div>
  );

  const renderError = () => (
    <div className="text-center py-8">
      <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
        Erreur lors du remboursement
      </h3>
      <p className="text-gray-600 dark:text-gray-400 mb-4">
        {error}
      </p>
      <button
        onClick={() => setStep('form')}
        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
      >
        Réessayer
      </button>
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
      >
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Remboursement de transaction
          </h2>
          {step !== 'processing' && (
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <X className="h-6 w-6" />
            </button>
          )}
        </div>

        <div className="p-6">
          {step === 'form' && renderForm()}
          {step === 'processing' && renderProcessing()}
          {step === 'success' && renderSuccess()}
          {step === 'error' && renderError()}
        </div>
      </motion.div>
    </div>
  );
}
