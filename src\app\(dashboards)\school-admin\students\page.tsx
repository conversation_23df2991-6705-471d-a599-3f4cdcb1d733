"use client";

import { GraduationCap, Presentation } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import CircularLoader from "@/components/widgets/CircularLoader";
import React, { Suspense} from "react";
import useAuth from "@/app/hooks/useAuth";
import StudentComponent from "@/components/Dashboard/ReusableComponents/StudentComponent";
import { SchoolAdminDashboardSkeleton } from "@/components/skeletons";

const BASE_URL = "/school-admin";

const navigation = {
  icon: GraduationCap,
  baseHref: `${BASE_URL}/students`,
  title: "Students"
};

export default function Page() {
  const { logout } = useAuth();
  const { user } = useAuth();
  return (
    <Suspense fallback={
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        <SchoolAdminDashboardSkeleton />
      </SchoolLayout>
    }>
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        {/* You might want to pass schoolId from somewhere here */}
        {user && <StudentComponent user={user} />}
      </SchoolLayout>
    </Suspense>
  );
}
