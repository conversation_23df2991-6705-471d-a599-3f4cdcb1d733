/**
 * Script de test pour l'endpoint des transactions problématiques
 */

const mongoose = require('mongoose');
const CreditPurchase = require('./src/models/CreditPurchase');
const School = require('./src/models/School'); // Importer le modèle School
const SchoolSubscription = require('./src/models/SchoolSubscription');
require('dotenv').config();

async function testProblematicTransactions() {
  try {
    console.log('🔍 Testing problematic transactions endpoint...');
    
    // Connexion à MongoDB
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');

    // Test direct de la logique
    const currentTime = new Date();
    const oneDayAgo = new Date(currentTime.getTime() - 24 * 60 * 60 * 1000);

    console.log('📅 Current time:', currentTime.toISOString());
    console.log('📅 One day ago:', oneDayAgo.toISOString());

    // Rechercher les transactions problématiques
    const problematicTransactions = await CreditPurchase.find({
      $or: [
        {
          payment_status: 'pending',
          purchase_date: { $lt: oneDayAgo }
        },
        {
          payment_status: 'failed'
        },
        {
          payment_status: 'expired'
        }
      ]
    })
    .populate('school_id', 'name email phone')
    .sort({ purchase_date: -1 })
    .limit(100);

    console.log(`📊 Found ${problematicTransactions.length} problematic transactions`);

    if (problematicTransactions.length > 0) {
      console.log('\n📋 Transactions details:');
      problematicTransactions.forEach((transaction, index) => {
        console.log(`${index + 1}. Transaction ID: ${transaction.transaction_id}`);
        console.log(`   School: ${transaction.school_id?.name || 'Unknown'}`);
        console.log(`   Status: ${transaction.payment_status}`);
        console.log(`   Amount: ${transaction.total_amount} XAF`);
        console.log(`   Date: ${transaction.purchase_date}`);
        console.log(`   Payment Method: ${transaction.payment_method}`);
        console.log('');
      });
    } else {
      console.log('✅ No problematic transactions found');
    }

    // Test de l'endpoint via HTTP
    console.log('\n🌐 Testing HTTP endpoint...');
    
    const fetch = require('node-fetch');
    const BASE_URL = 'http://localhost:3002/api';
    
    try {
      const response = await fetch(`${BASE_URL}/credit-purchase/problematic`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          // Note: En production, il faudrait un vrai token
          'Authorization': 'Bearer test_token'
        }
      });

      console.log(`📡 HTTP Status: ${response.status}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ HTTP Response: ${data.length} transactions returned`);
      } else {
        const errorData = await response.text();
        console.log(`❌ HTTP Error: ${errorData}`);
      }
    } catch (httpError) {
      console.log(`❌ HTTP Request failed: ${httpError.message}`);
      console.log('   (This is normal if the server is not running)');
    }

    // Vérifier les routes
    console.log('\n🛣️ Checking routes configuration...');
    
    try {
      const creditPurchaseController = require('./src/controllers/creditPurchaseController');
      const hasFunction = typeof creditPurchaseController.getProblematicTransactions === 'function';
      console.log(`✅ Controller function exists: ${hasFunction}`);
    } catch (routeError) {
      console.log(`❌ Route check failed: ${routeError.message}`);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

// Fonction pour créer une transaction de test si nécessaire
async function createTestTransaction() {
  try {
    console.log('🧪 Creating test transaction...');
    
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    // Créer une transaction en attente depuis plus d'un jour
    const testTransaction = new CreditPurchase({
      school_id: new mongoose.Types.ObjectId(), // ID fictif
      transaction_id: `TEST_${Date.now()}`,
      purchase_id: `PUR_TEST_${Date.now()}`,
      credits_purchased: 100,
      total_amount: 75000,
      payment_method: 'fapshi',
      payment_status: 'pending',
      purchase_date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // Il y a 2 jours
      billing_info: {
        name: 'Test User',
        email: '<EMAIL>',
        phone: '677123456'
      }
    });

    await testTransaction.save();
    console.log(`✅ Test transaction created: ${testTransaction.transaction_id}`);
    
  } catch (error) {
    console.error('❌ Failed to create test transaction:', error);
  } finally {
    await mongoose.disconnect();
  }
}

// Exécuter les tests
async function runTests() {
  const args = process.argv.slice(2);
  
  if (args.includes('--create-test')) {
    await createTestTransaction();
  } else {
    await testProblematicTransactions();
  }
}

if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testProblematicTransactions, createTestTransaction };
