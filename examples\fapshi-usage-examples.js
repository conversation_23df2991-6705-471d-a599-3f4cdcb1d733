/**
 * Exemples d'utilisation de l'intégration Fapshi
 * Ces exemples montrent comment utiliser toutes les fonctionnalités disponibles
 */

const fapshi = require('../src/utils/fapshi');

// Exemple 1: Initiation d'un paiement avec lien de redirection
async function exempleInitiatePay() {
  console.log('=== Exemple: Initiation de paiement ===');
  
  const paymentData = {
    amount: 5000,
    email: '<EMAIL>',
    userId: 'user_12345',
    externalId: 'order_67890',
    redirectUrl: 'https://monsite.com/success',
    message: 'Achat de 5 crédits scolaires'
  };

  try {
    const result = await fapshi.initiatePay(paymentData);
    
    if (result.statusCode === 200) {
      console.log('✅ Paiement initié avec succès');
      console.log('Lien de paiement:', result.link);
      console.log('Transaction ID:', result.transId);
      
      // Rediriger l'utilisateur vers result.link
      return result.transId;
    } else {
      console.error('❌ Erreur lors de l\'initiation:', result.message);
    }
  } catch (error) {
    console.error('❌ Erreur:', error.message);
  }
}

// Exemple 2: Paiement direct sur mobile
async function exempleDirectPay() {
  console.log('\n=== Exemple: Paiement direct ===');
  
  const directPayData = {
    amount: 3000,
    phone: '677123456',
    medium: 'mobile money',
    name: 'Jean Dupont',
    email: '<EMAIL>',
    userId: 'user_12345',
    externalId: 'direct_order_123',
    message: 'Paiement direct pour 3 crédits'
  };

  try {
    const result = await fapshi.directPay(directPayData);
    
    if (result.statusCode === 200) {
      console.log('✅ Paiement direct initié');
      console.log('Transaction ID:', result.transId);
      
      // Surveiller le statut de cette transaction
      return result.transId;
    } else {
      console.error('❌ Erreur paiement direct:', result.message);
    }
  } catch (error) {
    console.error('❌ Erreur:', error.message);
  }
}

// Exemple 3: Vérification du statut d'une transaction
async function exemplePaymentStatus(transId) {
  console.log('\n=== Exemple: Vérification du statut ===');
  
  try {
    const status = await fapshi.paymentStatus(transId);
    
    if (status.statusCode === 200) {
      console.log('✅ Statut récupéré');
      console.log('Transaction ID:', status.transId);
      console.log('Statut:', status.status);
      console.log('Montant:', status.amount, 'XAF');
      console.log('Email:', status.email);
      console.log('Date création:', status.dateInitiated);
      
      if (status.status === 'SUCCESSFUL') {
        console.log('🎉 Paiement réussi !');
        // Traiter le paiement réussi
      } else if (status.status === 'FAILED') {
        console.log('💔 Paiement échoué');
        // Gérer l'échec du paiement
      } else if (status.status === 'EXPIRED') {
        console.log('⏰ Paiement expiré');
        // Gérer l'expiration
      } else {
        console.log('⏳ Paiement en attente...');
        // Continuer à surveiller
      }
      
      return status;
    } else {
      console.error('❌ Erreur statut:', status.message);
    }
  } catch (error) {
    console.error('❌ Erreur:', error.message);
  }
}

// Exemple 4: Recherche de transactions avec filtres
async function exempleSearchTransactions() {
  console.log('\n=== Exemple: Recherche de transactions ===');
  
  const filters = {
    status: 'successful',
    medium: 'mobile money',
    start: '2023-01-01',
    end: '2023-12-31',
    limit: 20,
    sort: 'desc'
  };

  try {
    const results = await fapshi.searchTransactions(filters);
    
    if (results.statusCode === 200) {
      console.log('✅ Recherche réussie');
      console.log(`Nombre de transactions trouvées: ${results.length}`);
      
      results.forEach((trans, index) => {
        console.log(`${index + 1}. ${trans.transId} - ${trans.status} - ${trans.amount} XAF`);
      });
    } else {
      console.error('❌ Erreur recherche:', results.message);
    }
  } catch (error) {
    console.error('❌ Erreur:', error.message);
  }
}

// Exemple 5: Récupération du solde du service
async function exempleServiceBalance() {
  console.log('\n=== Exemple: Solde du service ===');
  
  try {
    const balance = await fapshi.getServiceBalance();
    
    if (balance.statusCode === 200) {
      console.log('✅ Solde récupéré');
      console.log(`Solde actuel: ${balance.balance} ${balance.currency}`);
      console.log(`Service: ${balance.service}`);
    } else {
      console.error('❌ Erreur solde:', balance.message);
    }
  } catch (error) {
    console.error('❌ Erreur:', error.message);
  }
}

// Exemple 6: Validation d'un webhook
async function exempleValidateWebhook(webhookData) {
  console.log('\n=== Exemple: Validation de webhook ===');
  
  try {
    const validation = await fapshi.validateWebhook(webhookData);
    
    if (validation.valid) {
      console.log('✅ Webhook valide');
      console.log('Transaction:', validation.transaction.transId);
      console.log('Nouveau statut:', validation.transaction.status);
      
      // Traiter le webhook
      await traiterWebhook(validation.transaction);
    } else {
      console.error('❌ Webhook invalide:', validation.message);
    }
  } catch (error) {
    console.error('❌ Erreur validation:', error.message);
  }
}

// Fonction helper pour traiter un webhook validé
async function traiterWebhook(transaction) {
  console.log('🔄 Traitement du webhook...');
  
  switch (transaction.status) {
    case 'SUCCESSFUL':
      console.log('💰 Paiement réussi - Créditer le compte');
      // Logique pour créditer le compte utilisateur
      break;
      
    case 'FAILED':
      console.log('❌ Paiement échoué - Notifier l\'utilisateur');
      // Logique pour notifier l'échec
      break;
      
    case 'EXPIRED':
      console.log('⏰ Paiement expiré - Nettoyer les données');
      // Logique pour nettoyer les données expirées
      break;
      
    default:
      console.log('ℹ️ Statut intermédiaire:', transaction.status);
  }
}

// Exemple 7: Expiration manuelle d'une transaction
async function exempleExpireTransaction(transId) {
  console.log('\n=== Exemple: Expiration de transaction ===');
  
  try {
    const result = await fapshi.expirePay(transId);
    
    if (result.statusCode === 200) {
      console.log('✅ Transaction expirée');
      console.log('Transaction ID:', result.transId);
      console.log('Nouveau statut:', result.status);
    } else {
      console.error('❌ Erreur expiration:', result.message);
    }
  } catch (error) {
    console.error('❌ Erreur:', error.message);
  }
}

// Exemple 8: Récupération des transactions d'un utilisateur
async function exempleUserTransactions(userId) {
  console.log('\n=== Exemple: Transactions utilisateur ===');
  
  try {
    const transactions = await fapshi.userTrans(userId);
    
    if (transactions.statusCode === 200) {
      console.log('✅ Transactions récupérées');
      console.log(`Nombre de transactions: ${transactions.length}`);
      
      transactions.forEach((trans, index) => {
        console.log(`${index + 1}. ${trans.transId} - ${trans.status} - ${trans.amount} XAF`);
      });
    } else {
      console.error('❌ Erreur transactions:', transactions.message);
    }
  } catch (error) {
    console.error('❌ Erreur:', error.message);
  }
}

// Exemple complet d'un flux de paiement
async function exempleFluxComplet() {
  console.log('\n🚀 === EXEMPLE COMPLET DE FLUX DE PAIEMENT ===');
  
  try {
    // 1. Initier le paiement
    const transId = await exempleInitiatePay();
    
    if (transId) {
      // 2. Simuler une attente puis vérifier le statut
      console.log('\n⏳ Attente de 5 secondes...');
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // 3. Vérifier le statut
      const status = await exemplePaymentStatus(transId);
      
      // 4. Si toujours en attente, on peut expirer manuellement
      if (status && status.status === 'CREATED') {
        console.log('\n🔄 Transaction toujours en attente, expiration...');
        await exempleExpireTransaction(transId);
      }
    }
    
    // 5. Autres exemples
    await exempleServiceBalance();
    await exempleSearchTransactions();
    
  } catch (error) {
    console.error('❌ Erreur dans le flux complet:', error.message);
  }
}

// Exporter les fonctions pour utilisation
module.exports = {
  exempleInitiatePay,
  exempleDirectPay,
  exemplePaymentStatus,
  exempleSearchTransactions,
  exempleServiceBalance,
  exempleValidateWebhook,
  exempleExpireTransaction,
  exempleUserTransactions,
  exempleFluxComplet
};

// Exécuter l'exemple complet si ce fichier est lancé directement
if (require.main === module) {
  exempleFluxComplet().then(() => {
    console.log('\n✅ Exemples terminés');
  }).catch(error => {
    console.error('\n❌ Erreur dans les exemples:', error);
  });
}
