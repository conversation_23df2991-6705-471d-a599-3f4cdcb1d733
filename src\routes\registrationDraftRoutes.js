const express = require('express');
const registrationDraftController = require('../controllers/registrationDraftController');
const { authenticate, authorize, checkSubscription } = require('../middleware/middleware');

const router = express.Router();

// New routes for school-specific registration drafts
router.get('/get/:school_id/user/:user_id', authenticate, authorize(['admin', 'super', 'school_admin', 'teacher']), registrationDraftController.getRegistrationDrafts);
router.post('/create/:school_id', authenticate, authorize(['admin', 'super', 'school_admin', 'dean_of_studies']), registrationDraftController.createRegistrationDraft);
router.put('/update/:school_id/:draft_id', authenticate, authorize(['admin', 'super', 'school_admin', 'dean_of_studies']), registrationDraftController.updateRegistrationDraft);
router.delete('/delete/:school_id/:draft_id', authenticate, authorize(['admin', 'super', 'school_admin', 'dean_of_studies']), registrationDraftController.deleteRegistrationDraft);

module.exports = router;
 