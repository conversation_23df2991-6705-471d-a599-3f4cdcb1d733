"use client";

import React, { useState, useEffect } from 'react';
import * as Tooltip from '@radix-ui/react-tooltip';
import { motion } from 'framer-motion';
import {
  ArrowLeft,
  Calendar,
  CreditCard,
  Download,
  Filter,
  Search,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  ExternalLink
} from 'lucide-react';
import Link from 'next/link';
import SchoolLayout from '@/components/Dashboard/Layouts/SchoolLayout';
import useAuth from '@/app/hooks/useAuth';
import { getCreditPurchaseHistory } from '@/app/services/SubscriptionServices';
import { formatCurrency, formatCredits } from '@/app/services/SubscriptionServices';
import { CreditPurchaseSchema } from '@/app/models/SchoolSubscriptionModel';
import CircularLoader from '@/components/widgets/CircularLoader';


const BASE_URL = "/school-admin";

export default function CreditHistoryPage() {
  const { user, logout } = useAuth();
  const [purchases, setPurchases] = useState<CreditPurchaseSchema[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('all');

  // Navigation avec titre dynamique
  const navigation = {
    icon: CreditCard,
    baseHref: `${BASE_URL}/buy-credit/history`,
    title: "Credits Purchase History"
  };
  useEffect(() => {
    if (user?.school_ids?.[0]) {
      fetchHistory();
    }
  }, [user]);

  const fetchHistory = async () => {
    try {
      setLoading(true);
      setError(null);

      const schoolId = user?.school_ids?.[0];
      if (!schoolId) {
        throw new Error('Aucune école associée');
      }

      const response = await getCreditPurchaseHistory(schoolId, 50, 0);
      setPurchases(response.purchases || []);
    } catch (err: any) {
      console.error('Error fetching history:', err);
      setError(err.message || 'Erreur lors du chargement de l\'historique');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'cancelled':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case 'expired':
        return <AlertTriangle className="h-5 w-5 text-orange-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Complété';
      case 'failed':
        return 'Échoué';
      case 'cancelled':
        return 'Annulé';
      case 'pending':
        return 'En attente';
      case 'expired':
        return 'Expiré';
      default:
        return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'failed':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      case 'expired':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
    }
  };

  // Fonction pour obtenir la raison d'annulation
  const getCancellationReason = (purchase: CreditPurchaseSchema): string => {
    if (purchase.payment_status !== 'cancelled') return '';

    // Vérifier dans les métadonnées
    if (purchase.metadata?.cancellation_reason) {
      return purchase.metadata.cancellation_reason;
    }

    // Vérifier dans les notes
    if (purchase.notes) {
      return purchase.notes;
    }

    return 'Raison non spécifiée';
  };

  const filteredPurchases = purchases.filter(purchase => {
    const matchesSearch = purchase.purchase_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      purchase.transaction_id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || purchase.payment_status === statusFilter;

    let matchesDate = true;
    if (dateFilter !== 'all') {
      const purchaseDate = new Date(purchase.purchase_date);
      const now = new Date();

      switch (dateFilter) {
        case 'today':
          matchesDate = purchaseDate.toDateString() === now.toDateString();
          break;
        case 'week':
          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          matchesDate = purchaseDate >= weekAgo;
          break;
        case 'month':
          const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          matchesDate = purchaseDate >= monthAgo;
          break;
      }
    }

    return matchesSearch && matchesStatus && matchesDate;
  });

  // Calculer les statistiques par statut
  const completedPurchases = filteredPurchases.filter(p => p.payment_status === 'completed');
  const pendingPurchases = filteredPurchases.filter(p => p.payment_status === 'pending');
  const cancelledPurchases = filteredPurchases.filter(p => p.payment_status === 'cancelled');
  const failedPurchases = filteredPurchases.filter(p => p.payment_status === 'failed');

  // Montant total : seulement les transactions completed
  const totalAmount = completedPurchases.reduce((sum, p) => sum + p.total_amount, 0);
  const totalCredits = completedPurchases.reduce((sum, p) => sum + p.credits_purchased, 0);

  // Statistiques pour pending et cancelled
  const pendingAmount = pendingPurchases.reduce((sum, p) => sum + p.total_amount, 0);
  const pendingCredits = pendingPurchases.reduce((sum, p) => sum + p.credits_purchased, 0);

  const cancelledAmount = cancelledPurchases.reduce((sum, p) => sum + p.total_amount, 0);
  const cancelledCredits = cancelledPurchases.reduce((sum, p) => sum + p.credits_purchased, 0);

  if (loading) {
    return (
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        <div className="flex items-center justify-center min-h-screen">
          <CircularLoader />
        </div>
      </SchoolLayout>
    );
  }

  return (
    <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
      <div className="p-6 max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link
              href="/school-admin/buy-credit"
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              <ArrowLeft className="h-5 w-5 text-gray-600 dark:text-gray-400" />
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Historique des achats
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Consultez l'historique de vos achats de crédits
              </p>
            </div>
          </div>

          <button className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
            <Download className="h-4 w-4 mr-2" />
            Exporter
          </button>
        </div>

        {/* Stats Summary */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          {/* Transactions complétées */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-full mr-4">
                <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Complétées</p>
                <p className="text-xl font-bold text-gray-900 dark:text-white">
                  {completedPurchases.length}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {formatCurrency(totalAmount)}
                </p>
              </div>
            </div>
          </div>

          {/* Transactions en attente */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-full mr-4">
                <Clock className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">En attente</p>
                <p className="text-xl font-bold text-gray-900 dark:text-white">
                  {pendingPurchases.length}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {formatCurrency(pendingAmount)}
                </p>
              </div>
            </div>
          </div>

          {/* Transactions annulées */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded-full mr-4">
                <XCircle className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Annulées</p>
                <p className="text-xl font-bold text-gray-900 dark:text-white">
                  {cancelledPurchases.length}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {formatCurrency(cancelledAmount)}
                </p>
              </div>
            </div>
          </div>

          {/* Crédits obtenus */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full mr-4">
                <CreditCard className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Crédits obtenus</p>
                <p className="text-xl font-bold text-gray-900 dark:text-white">
                  {formatCredits(totalCredits)}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Transactions complétées
                </p>
              </div>
            </div>
          </div>

          {/* Total des transactions */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-full mr-4">
                <Calendar className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total transactions</p>
                <p className="text-xl font-bold text-gray-900 dark:text-white">
                  {filteredPurchases.length}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Toutes catégories
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Rechercher par ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Tous les statuts</option>
              <option value="completed">Complété</option>
              <option value="pending">En attente</option>
              <option value="cancelled">Annulé</option>
              <option value="failed">Échoué</option>
              <option value="expired">Expiré</option>
            </select>

            <select
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Toutes les dates</option>
              <option value="today">Aujourd'hui</option>
              <option value="week">Cette semaine</option>
              <option value="month">Ce mois</option>
            </select>
          </div>
        </div>

        {/* Purchases List */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden">
          {error ? (
            <div className="p-6 text-center">
              <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <p className="text-red-600 dark:text-red-400">{error}</p>
              <button
                onClick={fetchHistory}
                className="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                Réessayer
              </button>
            </div>
          ) : filteredPurchases.length === 0 ? (
            <div className="p-6 text-center">
              <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400">Aucun achat trouvé</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Transaction
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Crédits
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Montant
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Statut
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {filteredPurchases.map((purchase) => (
                    <motion.tr
                      key={purchase._id}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="hover:bg-gray-50 dark:hover:bg-gray-700"
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {purchase.purchase_id}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {purchase.transaction_id}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {new Date(purchase.purchase_date).toLocaleDateString('fr-FR')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                        {formatCredits(purchase.credits_purchased)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {formatCurrency(purchase.total_amount)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {purchase.payment_status === 'cancelled' ? (
                          <Tooltip.Provider>
                            <Tooltip.Root>
                              <Tooltip.Trigger asChild>
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium cursor-help ${getStatusColor(purchase.payment_status)}`}>
                                  {getStatusIcon(purchase.payment_status)}
                                  <span className="ml-1">{getStatusText(purchase.payment_status)}</span>
                                </span>
                              </Tooltip.Trigger>
                              <Tooltip.Portal>
                                <Tooltip.Content
                                  className="bg-gray-900 text-white px-3 py-2 rounded-lg text-sm max-w-xs shadow-lg z-50"
                                  sideOffset={5}
                                >
                                  <div className="font-medium mb-1">Raison d'annulation:</div>
                                  <div>{getCancellationReason(purchase)}</div>
                                  <Tooltip.Arrow className="fill-gray-900" />
                                </Tooltip.Content>
                              </Tooltip.Portal>
                            </Tooltip.Root>
                          </Tooltip.Provider>
                        ) : (
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(purchase.payment_status)}`}>
                            {getStatusIcon(purchase.payment_status)}
                            <span className="ml-1">{getStatusText(purchase.payment_status)}</span>
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        {purchase.payment_status === 'pending' && purchase.payment_gateway_response?.link && (
                          <a
                            href={purchase.payment_gateway_response.link}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 flex items-center"
                          >
                            <ExternalLink className="h-4 w-4 mr-1" />
                            Finaliser
                          </a>
                        )}
                      </td>
                    </motion.tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </SchoolLayout>
  );
}
