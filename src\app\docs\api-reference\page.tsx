"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
    Code,
    Copy,
    Check,
    ExternalLink,
    Key,
    Globe,
    Lock,
    AlertCircle,
    Info,
    FileText,
    Database,
    Users,
    GraduationCap,
    BookOpen,
    Calendar,
    BarChart3,
    MessageCircle,
    CreditCard,
    Shield,
    ArrowLeft,
} from "lucide-react";

interface APIEndpoint {
    method: string;
    endpoint: string;
    description: string;
    params?: string;
    requestBody?: string;
    response: string;
}

export default function APIReferencePage() {
    const router = useRouter();
    const [copiedCode, setCopiedCode] = useState<string | null>(null);
    const [activeEndpoint, setActiveEndpoint] = useState("authentication");

    const copyToClipboard = async (text: string, id: string) => {
        try {
            await navigator.clipboard.writeText(text);
            setCopiedCode(id);
            setTimeout(() => setCopiedCode(null), 2000);
        } catch (err) {
            console.error("Failed to copy text: ", err);
        }
    };

    const endpointCategories = [
        {
            id: "authentication",
            title: "Authentication",
            icon: <Key className="w-5 h-5" />,
            description: "User authentication and authorization",
        },
        {
            id: "users",
            title: "Users",
            icon: <Users className="w-5 h-5" />,
            description: "User management endpoints",
        },
        {
            id: "students",
            title: "Students",
            icon: <GraduationCap className="w-5 h-5" />,
            description: "Student data and academic records",
        },
        {
            id: "schools",
            title: "Schools",
            icon: <BookOpen className="w-5 h-5" />,
            description: "School management and configuration",
        },
        {
            id: "classes",
            title: "Classes",
            icon: <Calendar className="w-5 h-5" />,
            description: "Class schedules and management",
        },
        {
            id: "analytics",
            title: "Analytics",
            icon: <BarChart3 className="w-5 h-5" />,
            description: "Reports and analytics data",
        },
    ];

    const authEndpoints: APIEndpoint[] = [
        {
            method: "POST",
            endpoint: "/api/auth/login",
            description: "Authenticate user and receive access token",
            requestBody: `{
  "email": "<EMAIL>",
  "password": "your_password"
}`,
            response: `{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "123",
    "email": "<EMAIL>",
    "role": "school_admin",
    "school_ids": ["school_456"]
  }
}`,
        },
        {
            method: "POST",
            endpoint: "/api/auth/refresh",
            description: "Refresh authentication token",
            requestBody: `{
  "refresh_token": "your_refresh_token"
}`,
            response: `{
  "success": true,
  "token": "new_access_token",
  "expires_in": 3600
}`,
        },
        {
            method: "POST",
            endpoint: "/api/auth/logout",
            description: "Logout user and invalidate token",
            requestBody: `{}`,
            response: `{
  "success": true,
  "message": "Successfully logged out"
}`,
        },
    ];

    const userEndpoints: APIEndpoint[] = [
        {
            method: "GET",
            endpoint: "/api/users",
            description: "Get list of users with pagination",
            params: "?page=1&limit=10&role=teacher",
            response: `{
  "success": true,
  "data": [
    {
      "id": "user_123",
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "teacher",
      "active": true,
      "created_at": "2024-01-15T10:30:00Z"
    }
  ],
  "pagination": {
    "current_page": 1,
    "total_pages": 10,
    "total_users": 95
  }
}`,
        },
        {
            method: "POST",
            endpoint: "/api/users",
            description: "Create a new user",
            requestBody: `{
  "name": "Jane Smith",
  "email": "<EMAIL>",
  "role": "teacher",
  "password": "temporary_password",
  "school_id": "school_456"
}`,
            response: `{
  "success": true,
  "data": {
    "id": "user_124",
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "role": "teacher",
    "active": true
  }
}`,
        },
        {
            method: "PUT",
            endpoint: "/api/users/{id}",
            description: "Update user information",
            requestBody: `{
  "name": "Jane Smith Updated",
  "email": "<EMAIL>",
  "active": false
}`,
            response: `{
  "success": true,
  "data": {
    "id": "user_124",
    "name": "Jane Smith Updated",
    "email": "<EMAIL>",
    "active": false
  }
}`,
        },
    ];

    const studentEndpoints: APIEndpoint[] = [
        {
            method: "GET",
            endpoint: "/api/students",
            description: "Get list of students",
            params: "?school_id=123&class_id=456",
            response: `{
  "success": true,
  "data": [
    {
      "id": "student_789",
      "name": "Alice Johnson",
      "email": "<EMAIL>",
      "student_id": "STU001",
      "class_id": "class_456",
      "grade_level": "Grade 10",
      "enrollment_date": "2024-01-15"
    }
  ]
}`,
        },
        {
            method: "GET",
            endpoint: "/api/students/{id}/grades",
            description: "Get student's academic grades",
            response: `{
  "success": true,
  "data": {
    "student_id": "student_789",
    "grades": [
      {
        "subject": "Mathematics",
        "term": "Term 1",
        "grade": "A",
        "score": 95,
        "teacher": "Mr. Smith"
      }
    ]
  }
}`,
        },
    ];

    const getCurrentEndpoints = (): APIEndpoint[] => {
        switch (activeEndpoint) {
            case "authentication":
                return authEndpoints;
            case "users":
                return userEndpoints;
            case "students":
                return studentEndpoints;
            default:
                return authEndpoints;
        }
    };

    return (
        <div className="p-8">
            {/* Back Button */}
            <div className="mb-6">
                <button
                    onClick={() => router.back()}
                    className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-teal-600 dark:hover:text-teal-300 transition-colors group"
                >
                    <ArrowLeft className="w-4 h-4 transition-transform group-hover:-translate-x-1" />
                    <span className="text-sm font-medium">
                        Back to Documentation
                    </span>
                </button>
            </div>

            {/* Header */}
            <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                    API Reference
                </h1>
                <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
                    Complete REST API documentation for the Scholarify platform.
                    This reference includes all available endpoints,
                    request/response formats, and authentication methods.
                </p>
            </div>

            {/* API Overview */}
            <div className="mb-8 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
                <div className="flex items-start space-x-3">
                    <Info className="w-5 h-5 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
                    <div>
                        <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
                            API Base URL
                        </h3>
                        <div className="bg-blue-100 dark:bg-blue-900/30 rounded p-3 mb-3">
                            <code className="text-blue-800 dark:text-blue-200 text-sm">
                                https://api.scholarify.com/v1
                            </code>
                        </div>
                        <p className="text-blue-800 dark:text-blue-200 text-sm">
                            All API requests should be made to this base URL.
                            Authentication is required for most endpoints except
                            public authentication endpoints.
                        </p>
                    </div>
                </div>
            </div>

            {/* API Key Information */}
            <div className="mb-8 grid md:grid-cols-2 gap-6">
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                    <div className="flex items-center space-x-3 mb-4">
                        <Lock className="w-6 h-6 text-green-600 dark:text-green-400" />
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                            Authentication
                        </h3>
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 mb-4">
                        Use Bearer token authentication for API requests.
                    </p>
                    <div className="bg-gray-100 dark:bg-gray-800 rounded p-3">
                        <code className="text-sm text-gray-800 dark:text-gray-200">
                            Authorization: Bearer YOUR_TOKEN_HERE
                        </code>
                    </div>
                </div>

                <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                    <div className="flex items-center space-x-3 mb-4">
                        <Globe className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                            Content Type
                        </h3>
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 mb-4">
                        All requests should use JSON format.
                    </p>
                    <div className="bg-gray-100 dark:bg-gray-800 rounded p-3">
                        <code className="text-sm text-gray-800 dark:text-gray-200">
                            Content-Type: application/json
                        </code>
                    </div>
                </div>
            </div>

            {/* Endpoint Categories */}
            <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                    API Endpoints
                </h2>
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
                    {endpointCategories.map((category) => (
                        <button
                            key={category.id}
                            onClick={() => setActiveEndpoint(category.id)}
                            className={`p-4 rounded-lg border text-left transition-all ${
                                activeEndpoint === category.id
                                    ? "border-teal-500 bg-teal-50 dark:bg-teal-900/20"
                                    : "border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"
                            }`}
                        >
                            <div className="flex items-center space-x-3 mb-2">
                                <div
                                    className={`${
                                        activeEndpoint === category.id
                                            ? "text-teal-600 dark:text-teal-400"
                                            : "text-gray-500 dark:text-gray-400"
                                    }`}
                                >
                                    {category.icon}
                                </div>
                                <h3
                                    className={`font-semibold ${
                                        activeEndpoint === category.id
                                            ? "text-teal-900 dark:text-teal-100"
                                            : "text-gray-900 dark:text-white"
                                    }`}
                                >
                                    {category.title}
                                </h3>
                            </div>
                            <p
                                className={`text-sm ${
                                    activeEndpoint === category.id
                                        ? "text-teal-700 dark:text-teal-300"
                                        : "text-gray-600 dark:text-gray-400"
                                }`}
                            >
                                {category.description}
                            </p>
                        </button>
                    ))}
                </div>
            </div>

            {/* API Endpoints */}
            <div className="space-y-6">
                {getCurrentEndpoints().map((endpoint, index) => (
                    <div
                        key={index}
                        className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden"
                    >
                        <div className="bg-gray-50 dark:bg-gray-800 p-4 border-b border-gray-200 dark:border-gray-700">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-4">
                                    <span
                                        className={`px-3 py-1 rounded text-xs font-medium ${
                                            endpoint.method === "GET"
                                                ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
                                                : endpoint.method === "POST"
                                                ? "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"
                                                : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400"
                                        }`}
                                    >
                                        {endpoint.method}
                                    </span>
                                    <code className="text-gray-900 dark:text-white font-mono">
                                        {endpoint.endpoint}
                                        {endpoint.params && (
                                            <span className="text-gray-500">
                                                {endpoint.params}
                                            </span>
                                        )}
                                    </code>
                                </div>
                            </div>
                            <p className="text-gray-600 dark:text-gray-400 mt-2">
                                {endpoint.description}
                            </p>
                        </div>

                        <div className="p-4 space-y-4">
                            {endpoint.requestBody && (
                                <div>
                                    <div className="flex items-center justify-between mb-2">
                                        <h4 className="font-semibold text-gray-900 dark:text-white text-sm">
                                            Request Body
                                        </h4>
                                        <button
                                            onClick={() =>
                                                copyToClipboard(
                                                    endpoint.requestBody!,
                                                    `req-${index}`
                                                )
                                            }
                                            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                                        >
                                            {copiedCode === `req-${index}` ? (
                                                <Check className="w-4 h-4 text-green-500" />
                                            ) : (
                                                <Copy className="w-4 h-4" />
                                            )}
                                        </button>
                                    </div>
                                    <div className="bg-gray-900 dark:bg-gray-950 rounded p-4 overflow-x-auto">
                                        <pre className="text-green-400 text-sm">
                                            <code>{endpoint.requestBody}</code>
                                        </pre>
                                    </div>
                                </div>
                            )}

                            <div>
                                <div className="flex items-center justify-between mb-2">
                                    <h4 className="font-semibold text-gray-900 dark:text-white text-sm">
                                        Response
                                    </h4>
                                    <button
                                        onClick={() =>
                                            copyToClipboard(
                                                endpoint.response,
                                                `res-${index}`
                                            )
                                        }
                                        className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                                    >
                                        {copiedCode === `res-${index}` ? (
                                            <Check className="w-4 h-4 text-green-500" />
                                        ) : (
                                            <Copy className="w-4 h-4" />
                                        )}
                                    </button>
                                </div>
                                <div className="bg-gray-900 dark:bg-gray-950 rounded p-4 overflow-x-auto">
                                    <pre className="text-blue-400 text-sm">
                                        <code>{endpoint.response}</code>
                                    </pre>
                                </div>
                            </div>
                        </div>
                    </div>
                ))}
            </div>

            {/* Error Codes */}
            <div className="mt-12 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                    HTTP Status Codes
                </h3>
                <div className="grid md:grid-cols-2 gap-4">
                    {[
                        { code: "200", description: "OK - Request successful" },
                        {
                            code: "201",
                            description:
                                "Created - Resource created successfully",
                        },
                        {
                            code: "400",
                            description: "Bad Request - Invalid request format",
                        },
                        {
                            code: "401",
                            description:
                                "Unauthorized - Authentication required",
                        },
                        {
                            code: "403",
                            description: "Forbidden - Access denied",
                        },
                        {
                            code: "404",
                            description: "Not Found - Resource not found",
                        },
                        {
                            code: "500",
                            description: "Server Error - Internal server error",
                        },
                    ].map((status, index) => (
                        <div
                            key={index}
                            className="flex items-center space-x-3"
                        >
                            <code className="bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm font-mono">
                                {status.code}
                            </code>
                            <span className="text-gray-600 dark:text-gray-400 text-sm">
                                {status.description}
                            </span>
                        </div>
                    ))}
                </div>
            </div>

            {/* Rate Limiting */}
            <div className="mt-8 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6">
                <div className="flex items-start space-x-3">
                    <AlertCircle className="w-5 h-5 text-yellow-600 dark:text-yellow-400 flex-shrink-0 mt-0.5" />
                    <div>
                        <h3 className="font-semibold text-yellow-900 dark:text-yellow-100 mb-2">
                            Rate Limiting
                        </h3>
                        <p className="text-yellow-800 dark:text-yellow-200 text-sm">
                            API requests are limited to 1000 requests per hour
                            per API key. Rate limit information is included in
                            response headers.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );
}
